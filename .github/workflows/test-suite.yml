name: Test Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  PYTHON_VERSION: "3.12"

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_USER: test_user
          POSTGRES_PASSWORD: test_password
          POSTGRES_DB: test_oms_db
        ports:
          - 5433:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      redis:
        image: redis:7-alpine
        ports:
          - 6380:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'

    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y libpq-dev gcc

    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: Create mock Firebase credentials
      run: |
        mkdir -p application/app/auth
        echo '{"type": "service_account", "project_id": "test-project"}' > application/app/auth/firebase_app.json
        echo '{"type": "service_account", "project_id": "test-project"}' > application/app/auth/firebase_pos.json

    - name: Set up environment variables
      run: |
        echo "TESTING=true" >> $GITHUB_ENV
        echo "DEBUG=true" >> $GITHUB_ENV
        echo "DATABASE_URL=postgresql://test_user:test_password@localhost:5433/test_oms_db" >> $GITHUB_ENV
        echo "ALEMBIC_DATABASE_URL=postgresql://test_user:test_password@localhost:5433/test_oms_db" >> $GITHUB_ENV
        echo "REDIS_URL=redis://localhost:6380/0" >> $GITHUB_ENV
        echo "STOCK_CHECK_ENABLED=false" >> $GITHUB_ENV
        echo "WMS_INTEGRATION_ENABLED=false" >> $GITHUB_ENV
        echo "RAZORPAY_INTEGRATION_ENABLED=false" >> $GITHUB_ENV
        echo "SENTRY_ENABLED=false" >> $GITHUB_ENV
        echo "OTEL_ENABLED=false" >> $GITHUB_ENV
        echo "ALLOWED_ORIGINS=*" >> $GITHUB_ENV
        echo "RAZORPAY_KEY_ID=test_key_id" >> $GITHUB_ENV
        echo "RAZORPAY_KEY_SECRET=test_key_secret" >> $GITHUB_ENV
        echo "RAZORPAY_WEBHOOK_SECRET=test_webhook_secret" >> $GITHUB_ENV

    - name: Run database migrations
      run: |
        cd application
        alembic upgrade head

    - name: Run unit tests
      run: |
        cd application
        pytest -m unit --cov=app --cov-report=xml --cov-report=term-missing -v

    - name: Run integration tests
      run: |
        cd application
        pytest -m integration --cov=app --cov-append --cov-report=xml --cov-report=term-missing -v

    - name: Run API tests
      run: |
        cd application
        pytest -m api --cov=app --cov-append --cov-report=xml --cov-report=term-missing -v

    - name: Run database tests
      run: |
        cd application
        pytest -m database --cov=app --cov-append --cov-report=xml --cov-report=term-missing -v

    - name: Run authentication tests
      run: |
        cd application
        pytest -m auth --cov=app --cov-append --cov-report=xml --cov-report=term-missing -v

    - name: Run external service tests
      run: |
        cd application
        pytest -m external --cov=app --cov-append --cov-report=xml --cov-report=term-missing -v

    - name: Generate final coverage report
      run: |
        cd application
        pytest --cov=app --cov-report=html --cov-report=xml --cov-report=term-missing --cov-fail-under=80

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./application/coverage.xml
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: true

    - name: Archive test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: test-results
        path: |
          application/htmlcov/
          application/coverage.xml
          application/reports/

    - name: Archive coverage report
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: coverage-report
        path: application/htmlcov/

  docker-test:
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Build test image
      run: |
        docker build -f Dockerfile.test -t rozana-oms-test .

    - name: Run Docker-based tests
      run: |
        docker-compose -f docker-compose.test.yml up -d test-db test-redis
        sleep 10  # Wait for services to be ready
        docker-compose -f docker-compose.test.yml run --rm test-runner pytest --maxfail=5 --tb=short

    - name: Clean up Docker resources
      if: always()
      run: |
        docker-compose -f docker-compose.test.yml down -v

  security-scan:
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install security scanning tools
      run: |
        pip install safety bandit

    - name: Run safety check
      run: |
        safety check -r requirements.txt

    - name: Run bandit security scan
      run: |
        bandit -r application/app/ -f json -o bandit-report.json || true

    - name: Upload security scan results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: security-scan-results
        path: bandit-report.json

  performance-test:
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install locust

    - name: Start services for performance testing
      run: |
        docker-compose -f docker-compose.test.yml up -d test-db test-redis
        sleep 10

    - name: Run performance tests
      run: |
        cd application
        # Run a quick performance test
        timeout 60s locust -f tests/performance/locustfile.py --headless -u 10 -r 2 -t 30s --host http://localhost:8000 || true

    - name: Clean up
      if: always()
      run: |
        docker-compose -f docker-compose.test.yml down -v

  notify:
    runs-on: ubuntu-latest
    needs: [test, docker-test, security-scan]
    if: always()
    
    steps:
    - name: Notify on success
      if: needs.test.result == 'success' && needs.docker-test.result == 'success'
      run: |
        echo "✅ All tests passed successfully!"
        
    - name: Notify on failure
      if: needs.test.result == 'failure' || needs.docker-test.result == 'failure'
      run: |
        echo "❌ Tests failed. Please check the logs."
        exit 1
