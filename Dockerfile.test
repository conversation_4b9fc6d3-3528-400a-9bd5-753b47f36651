FROM python:3.12-slim

# Set working directory
WORKDIR /application

# Install system dependencies
RUN apt-get update && apt-get install -y \
    libpq-dev \
    gcc \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt /application/requirements.txt
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY application /application

# Create directories for test reports
RUN mkdir -p /application/reports /application/htmlcov

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONPATH=/application
ENV TESTING=true

# Create mock Firebase credentials for testing
RUN mkdir -p /application/app/auth && \
    echo '{"type": "service_account", "project_id": "test-project"}' > /application/app/auth/firebase_app.json && \
    echo '{"type": "service_account", "project_id": "test-project"}' > /application/app/auth/firebase_pos.json

# Default command runs tests
CMD ["pytest", "--maxfail=5", "--tb=short", "-v"]
