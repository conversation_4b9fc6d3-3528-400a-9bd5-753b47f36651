[tool:pytest]
minversion = 6.0
addopts = 
    -ra
    --strict-markers
    --strict-config
    --cov=application/app
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml:coverage.xml
    --cov-fail-under=80
    --html=reports/pytest_report.html
    --self-contained-html
    -v
testpaths = 
    application/app/tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
markers =
    unit: Unit tests
    integration: Integration tests
    auth: Authentication tests
    database: Database tests
    api: API endpoint tests
    external: External service tests
    slow: Slow running tests
    firebase: Firebase authentication tests
    razorpay: Razorpay payment tests
    wms: WMS integration tests
asyncio_mode = auto
asyncio_default_fixture_loop_scope = function
env =
    TESTING = true
    DEBUG = true
    DATABASE_URL = postgresql://test_user:test_password@localhost:5433/test_oms_db
    ALEMBIC_DATABASE_URL = postgresql://test_user:test_password@localhost:5433/test_oms_db
    REDIS_URL = redis://localhost:6380/0
    STOCK_CHECK_ENABLED = false
    WMS_INTEGRATION_ENABLED = false
    RAZORPAY_INTEGRATION_ENABLED = false
    SENTRY_ENABLED = false
    OTEL_ENABLED = false
    ALLOWED_ORIGINS = *
    TOKEN_VALIDATION_URL = http://localhost:8001
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore:.*unclosed.*:ResourceWarning
    ignore:.*unclosed.*:DeprecationWarning
