# Rozana OMS Service - Testing Documentation

This document provides comprehensive instructions for running the test suite for the Rozana OMS service.

## Overview

The test suite provides comprehensive coverage for:
- **Authentication**: Firebase middleware, external auth endpoints
- **API Endpoints**: All routes (app, pos, web, api, health, payments, webhooks)
- **Core Services**: Order management, payment processing, query services
- **Database**: Models, connections, migrations, data integrity
- **Integrations**: WMS, Razorpay, Firebase, external services
- **End-to-End Workflows**: Complete order and payment flows

## Test Infrastructure

### Technology Stack
- **pytest**: Main testing framework
- **pytest-asyncio**: Async test support
- **pytest-cov**: Code coverage reporting
- **pytest-mock**: Mocking utilities
- **httpx**: Async HTTP client for testing
- **Docker**: Containerized test environment
- **PostgreSQL**: Test database
- **Redis**: Test cache
- **WireMock**: External service mocking

### Test Categories
Tests are organized with markers for easy filtering:
- `unit`: Unit tests for individual components
- `integration`: Integration tests with external services
- `auth`: Authentication-related tests
- `database`: Database and model tests
- `api`: API endpoint tests
- `external`: External service integration tests
- `firebase`: Firebase authentication tests
- `razorpay`: Razorpay payment tests
- `wms`: WMS integration tests
- `slow`: Long-running tests

## Quick Start

### Prerequisites
- Docker and Docker Compose
- Python 3.12+
- Git

### Running Tests with Docker (Recommended)

1. **Start test environment:**
```bash
docker-compose -f docker-compose.test.yml up -d test-db test-redis mock-services
```

2. **Run all tests:**
```bash
docker-compose -f docker-compose.test.yml run --rm test-runner
```

3. **Run specific test categories:**
```bash
# Unit tests only
docker-compose -f docker-compose.test.yml run --rm test-runner pytest -m unit

# Authentication tests
docker-compose -f docker-compose.test.yml run --rm test-runner pytest -m auth

# API endpoint tests
docker-compose -f docker-compose.test.yml run --rm test-runner pytest -m api

# Integration tests (slower)
docker-compose -f docker-compose.test.yml run --rm test-runner pytest -m integration
```

4. **Generate coverage report:**
```bash
docker-compose -f docker-compose.test.yml run --rm test-runner pytest --cov-report=html
```

5. **Clean up:**
```bash
docker-compose -f docker-compose.test.yml down -v
```

### Running Tests Locally

1. **Install dependencies:**
```bash
pip install -r requirements.txt
```

2. **Set up test database:**
```bash
# Start PostgreSQL and Redis
docker-compose -f docker-compose.test.yml up -d test-db test-redis

# Run migrations
export DATABASE_URL="postgresql://test_user:test_password@localhost:5433/test_oms_db"
cd application && alembic upgrade head
```

3. **Run tests:**
```bash
cd application && pytest
```

## Test Configuration

### Environment Variables
The test suite uses these environment variables (automatically set in Docker):

```bash
TESTING=true
DEBUG=true
DATABASE_URL=postgresql://test_user:test_password@localhost:5433/test_oms_db
REDIS_URL=redis://localhost:6380/0
STOCK_CHECK_ENABLED=false
WMS_INTEGRATION_ENABLED=false
RAZORPAY_INTEGRATION_ENABLED=false
SENTRY_ENABLED=false
OTEL_ENABLED=false
```

### pytest Configuration
Key settings in `pytest.ini`:
- Minimum coverage: 80%
- HTML and XML coverage reports
- Async test support
- Custom markers for test categorization

## Test Structure

```
application/app/tests/
├── conftest.py                 # Shared fixtures and configuration
├── test_auth.py               # Authentication tests
├── test_services.py           # Core service tests
├── test_api_endpoints.py      # API endpoint tests
├── test_database.py           # Database and model tests
├── test_integrations.py       # Integration tests
└── test_external_auth.py      # External auth endpoint tests
```

## Key Test Features

### Authentication Testing
- Firebase middleware for app and POS routes
- External Firebase token refresh endpoint
- Google Identity Toolkit sign-in endpoint
- Token validation and error handling
- Security considerations and rate limiting

### Database Testing
- Transaction rollback for test isolation
- Model validation and constraints
- Relationship testing
- Index performance verification
- Migration compatibility

### API Testing
- All endpoint coverage with authentication
- Request/response validation
- Error handling (401, 404, 422, 500)
- CORS and middleware testing

### Integration Testing
- WMS service integration
- Razorpay payment gateway
- Firebase authentication flows
- End-to-end order workflows
- External service resilience

## Coverage Reports

### Viewing Coverage
After running tests with coverage:

1. **HTML Report:**
```bash
# Generated in htmlcov/ directory
open htmlcov/index.html
```

2. **Terminal Report:**
```bash
pytest --cov-report=term-missing
```

3. **XML Report (for CI/CD):**
```bash
pytest --cov-report=xml:coverage.xml
```

### Coverage Targets
- **Minimum**: 80% overall coverage
- **Target**: 90%+ for core business logic
- **Critical paths**: 100% for payment and order flows

## CI/CD Integration

### GitHub Actions
The test suite is designed for CI/CD integration. Example workflow:

```yaml
name: Test Suite
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Run Test Suite
        run: |
          docker-compose -f docker-compose.test.yml up -d test-db test-redis
          docker-compose -f docker-compose.test.yml run --rm test-runner
          
      - name: Upload Coverage
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage.xml
```

### Test Artifacts
- Coverage reports (HTML, XML)
- Test results (JUnit XML)
- Performance metrics
- Log files

## Troubleshooting

### Common Issues

1. **Database Connection Errors:**
```bash
# Ensure test database is running
docker-compose -f docker-compose.test.yml up -d test-db
# Check database health
docker-compose -f docker-compose.test.yml exec test-db pg_isready -U test_user
```

2. **Port Conflicts:**
```bash
# Check if ports are in use
lsof -i :5433  # Test database
lsof -i :6380  # Test Redis
# Stop conflicting services or change ports in docker-compose.test.yml
```

3. **Permission Issues:**
```bash
# Fix file permissions
chmod +x application/docker-entrypoint.sh
# Ensure Docker has access to volumes
```

4. **Memory Issues:**
```bash
# Increase Docker memory limit
# Or run tests in smaller batches
pytest --maxfail=1 -x
```

### Debug Mode
For debugging failing tests:

```bash
# Run with verbose output
pytest -v -s

# Run specific test with debugging
pytest -v -s application/app/tests/test_auth.py::TestFirebaseAuthMiddlewareAPP::test_middleware_accepts_valid_token

# Use pdb for interactive debugging
pytest --pdb
```

## Security Considerations

### Test Data
- All API keys and tokens in tests are mock values
- No production credentials are used
- Sensitive data is properly masked in logs

### External Services
- All external API calls are mocked
- No real Firebase or Razorpay calls in tests
- WireMock used for external service simulation

### Database
- Test database is isolated from production
- Automatic cleanup after each test
- No persistent test data

## Performance Testing

### Load Testing
For performance testing beyond unit tests:

```bash
# Install additional tools
pip install locust

# Run load tests (separate from unit tests)
locust -f tests/performance/locustfile.py
```

### Benchmarking
```bash
# Run with timing
pytest --benchmark-only

# Profile slow tests
pytest --profile
```

## Contributing

### Adding New Tests
1. Follow existing test structure and naming conventions
2. Use appropriate markers (`@pytest.mark.unit`, etc.)
3. Include docstrings explaining test purpose
4. Mock external dependencies
5. Ensure tests are deterministic and isolated

### Test Guidelines
- One assertion per test when possible
- Clear, descriptive test names
- Proper setup and teardown
- Mock external dependencies
- Test both success and failure cases

### Code Coverage
- Aim for high coverage on new code
- Don't sacrifice test quality for coverage numbers
- Focus on testing critical business logic
- Document any intentionally uncovered code
