# Rozana OMS Service - Test Management Makefile

.PHONY: help test test-unit test-integration test-api test-auth test-db test-external
.PHONY: test-docker test-local test-coverage test-watch test-debug
.PHONY: setup-test teardown-test clean-test
.PHONY: lint format security-scan
.PHONY: docker-build docker-test docker-clean

# Default target
help:
	@echo "Rozana OMS Service - Test Management"
	@echo "===================================="
	@echo ""
	@echo "Test Commands:"
	@echo "  test              - Run all tests with Docker"
	@echo "  test-unit         - Run unit tests only"
	@echo "  test-integration  - Run integration tests only"
	@echo "  test-api          - Run API endpoint tests only"
	@echo "  test-auth         - Run authentication tests only"
	@echo "  test-db           - Run database tests only"
	@echo "  test-external     - Run external service tests only"
	@echo "  test-local        - Run tests locally (requires setup)"
	@echo "  test-docker       - Run tests in Docker containers"
	@echo "  test-coverage     - Run tests with coverage report"
	@echo "  test-watch        - Run tests in watch mode"
	@echo "  test-debug        - Run tests with debugging enabled"
	@echo ""
	@echo "Setup Commands:"
	@echo "  setup-test        - Set up test environment"
	@echo "  teardown-test     - Tear down test environment"
	@echo "  clean-test        - Clean test artifacts"
	@echo ""
	@echo "Quality Commands:"
	@echo "  lint              - Run code linting"
	@echo "  format            - Format code"
	@echo "  security-scan     - Run security scans"
	@echo ""
	@echo "Docker Commands:"
	@echo "  docker-build      - Build test Docker image"
	@echo "  docker-test       - Run tests in Docker"
	@echo "  docker-clean      - Clean Docker test resources"

# Test Commands
test: docker-test

test-unit:
	@echo "Running unit tests..."
	docker-compose -f docker-compose.test.yml run --rm test-runner pytest -m unit -v

test-integration:
	@echo "Running integration tests..."
	docker-compose -f docker-compose.test.yml run --rm test-runner pytest -m integration -v

test-api:
	@echo "Running API tests..."
	docker-compose -f docker-compose.test.yml run --rm test-runner pytest -m api -v

test-auth:
	@echo "Running authentication tests..."
	docker-compose -f docker-compose.test.yml run --rm test-runner pytest -m auth -v

test-db:
	@echo "Running database tests..."
	docker-compose -f docker-compose.test.yml run --rm test-runner pytest -m database -v

test-external:
	@echo "Running external service tests..."
	docker-compose -f docker-compose.test.yml run --rm test-runner pytest -m external -v

test-local:
	@echo "Running tests locally..."
	@echo "Make sure test services are running: make setup-test"
	cd application && pytest -v

test-docker: setup-test
	@echo "Running all tests in Docker..."
	docker-compose -f docker-compose.test.yml run --rm test-runner pytest --maxfail=5 --tb=short -v

test-coverage: setup-test
	@echo "Running tests with coverage..."
	docker-compose -f docker-compose.test.yml run --rm test-runner pytest --cov=app --cov-report=html --cov-report=term-missing --cov-fail-under=80
	@echo "Coverage report generated in htmlcov/"

test-watch:
	@echo "Running tests in watch mode..."
	docker-compose -f docker-compose.test.yml run --rm test-runner pytest -f

test-debug:
	@echo "Running tests with debugging..."
	docker-compose -f docker-compose.test.yml run --rm test-runner pytest -v -s --pdb

# Setup Commands
setup-test:
	@echo "Setting up test environment..."
	docker-compose -f docker-compose.test.yml up -d test-db test-redis mock-services
	@echo "Waiting for services to be ready..."
	sleep 10
	@echo "Test environment ready!"

teardown-test:
	@echo "Tearing down test environment..."
	docker-compose -f docker-compose.test.yml down -v
	@echo "Test environment cleaned up!"

clean-test:
	@echo "Cleaning test artifacts..."
	rm -rf application/htmlcov/
	rm -rf application/reports/
	rm -f application/coverage.xml
	rm -rf application/.pytest_cache/
	find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	find . -type f -name "*.pyc" -delete 2>/dev/null || true
	@echo "Test artifacts cleaned!"

# Quality Commands
lint:
	@echo "Running code linting..."
	docker run --rm -v $(PWD):/app -w /app python:3.12-slim sh -c "pip install flake8 black isort && flake8 application/app/ && black --check application/app/ && isort --check-only application/app/"

format:
	@echo "Formatting code..."
	docker run --rm -v $(PWD):/app -w /app python:3.12-slim sh -c "pip install black isort && black application/app/ && isort application/app/"

security-scan:
	@echo "Running security scans..."
	docker run --rm -v $(PWD):/app -w /app python:3.12-slim sh -c "pip install safety bandit && safety check -r requirements.txt && bandit -r application/app/"

# Docker Commands
docker-build:
	@echo "Building test Docker image..."
	docker build -f Dockerfile.test -t rozana-oms-test .

docker-test: docker-build setup-test
	@echo "Running tests in Docker..."
	docker run --rm --network host -v $(PWD)/application:/application -e DATABASE_URL=postgresql://test_user:test_password@localhost:5433/test_oms_db -e REDIS_URL=redis://localhost:6380/0 rozana-oms-test

docker-clean:
	@echo "Cleaning Docker test resources..."
	docker-compose -f docker-compose.test.yml down -v --remove-orphans
	docker rmi rozana-oms-test 2>/dev/null || true
	docker system prune -f

# Performance Testing
test-performance: setup-test
	@echo "Running performance tests..."
	docker-compose -f docker-compose.test.yml run --rm test-runner sh -c "pip install locust && locust -f tests/performance/locustfile.py --headless -u 10 -r 2 -t 30s --host http://localhost:8000"

# Database Management
db-migrate:
	@echo "Running database migrations..."
	docker-compose -f docker-compose.test.yml run --rm test-runner alembic upgrade head

db-reset:
	@echo "Resetting test database..."
	docker-compose -f docker-compose.test.yml down -v test-db
	docker-compose -f docker-compose.test.yml up -d test-db
	sleep 5
	$(MAKE) db-migrate

# Continuous Integration Simulation
ci-test:
	@echo "Running CI test simulation..."
	$(MAKE) clean-test
	$(MAKE) setup-test
	$(MAKE) test-unit
	$(MAKE) test-integration
	$(MAKE) test-api
	$(MAKE) test-auth
	$(MAKE) test-db
	$(MAKE) test-external
	$(MAKE) test-coverage
	$(MAKE) security-scan
	$(MAKE) teardown-test
	@echo "CI test simulation completed!"

# Development Helpers
dev-setup:
	@echo "Setting up development environment..."
	pip install -r requirements.txt
	$(MAKE) setup-test
	@echo "Development environment ready!"

dev-test:
	@echo "Running quick development tests..."
	docker-compose -f docker-compose.test.yml run --rm test-runner pytest -x --ff

# Monitoring and Reporting
test-report:
	@echo "Generating test report..."
	docker-compose -f docker-compose.test.yml run --rm test-runner pytest --html=reports/pytest_report.html --self-contained-html
	@echo "Test report generated in reports/"

test-metrics:
	@echo "Collecting test metrics..."
	docker-compose -f docker-compose.test.yml run --rm test-runner pytest --benchmark-only --benchmark-json=reports/benchmark.json
	@echo "Test metrics collected in reports/"

# Utility Commands
logs:
	@echo "Showing test service logs..."
	docker-compose -f docker-compose.test.yml logs -f

status:
	@echo "Checking test service status..."
	docker-compose -f docker-compose.test.yml ps

shell:
	@echo "Opening test environment shell..."
	docker-compose -f docker-compose.test.yml run --rm test-runner bash

# Quick Commands for Common Tasks
quick-test: setup-test test-unit teardown-test
full-test: setup-test test-coverage teardown-test
smoke-test: setup-test test-api teardown-test
