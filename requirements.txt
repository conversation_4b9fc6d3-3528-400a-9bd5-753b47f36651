fastapi==0.115.0
uvicorn==0.30.6
psycopg==3.2.9
psycopg-pool==3.2.6
python-dotenv==1.0.1
pydantic==2.9.2
firebase-admin==6.0.0
sqlalchemy==2.0.41
asyncpg==0.30.0
alembic==1.16.2
gunicorn==23.0.0
httpx==0.27.0
redis
pytz==2023.3
razorpay==1.4.2
pycryptodome==3.19.0
setuptools==80.9.0
sentry-sdk[fastapi]==2.8.0
opentelemetry-api
opentelemetry-sdk
opentelemetry-exporter-otlp
opentelemetry-instrumentation
opentelemetry-instrumentation-fastapi
opentelemetry-instrumentation-httpx
opentelemetry-instrumentation-psycopg2
opentelemetry-instrumentation-redis
opentelemetry-instrumentation-logging
opentelemetry-exporter-otlp-proto-grpc
opentelemetry-sdk-extension-aws

# Testing dependencies
pytest==8.3.3
pytest-asyncio==0.24.0
pytest-mock==3.14.0
pytest-cov==5.0.0
pytest-xdist==3.6.0
pytest-html==4.1.1
pytest-env==1.1.5
pytest-docker==3.1.1
httpx==0.27.0
faker==30.8.2
factory-boy==3.3.1
responses==0.25.3
freezegun==1.5.1

