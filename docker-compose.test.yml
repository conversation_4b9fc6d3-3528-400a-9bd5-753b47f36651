version: '3.8'

services:
  # Test PostgreSQL Database
  test-db:
    image: postgres:15
    container_name: oms-test-db
    environment:
      POSTGRES_USER: test_user
      POSTGRES_PASSWORD: test_password
      POSTGRES_DB: test_oms_db
    ports:
      - "5433:5432"
    volumes:
      - test-db-data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U test_user -d test_oms_db"]
      interval: 5s
      timeout: 3s
      retries: 5
    restart: unless-stopped
    networks:
      - test-network

  # Test Redis
  test-redis:
    image: redis:7-alpine
    container_name: oms-test-redis
    ports:
      - "6380:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 5
    restart: unless-stopped
    networks:
      - test-network

  # Mock External Services
  mock-services:
    image: wiremock/wiremock:3.3.1
    container_name: oms-mock-services
    ports:
      - "8080:8080"
    volumes:
      - ./tests/mocks/wiremock:/home/<USER>
    command: ["--global-response-templating", "--verbose"]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/__admin/health"]
      interval: 5s
      timeout: 3s
      retries: 5
    networks:
      - test-network

  # Test Runner
  test-runner:
    build:
      context: .
      dockerfile: Dockerfile.test
    container_name: oms-test-runner
    environment:
      - TESTING=true
      - DEBUG=true
      - DATABASE_URL=*************************************************/test_oms_db
      - REDIS_URL=redis://test-redis:6379/0
      - STOCK_CHECK_ENABLED=false
      - WMS_INTEGRATION_ENABLED=false
      - RAZORPAY_INTEGRATION_ENABLED=false
      - SENTRY_ENABLED=false
      - OTEL_ENABLED=false
      - ALLOWED_ORIGINS=*
      - TOKEN_VALIDATION_URL=http://mock-services:8080
      - RAZORPAY_KEY_ID=test_key_id
      - RAZORPAY_KEY_SECRET=test_key_secret
      - RAZORPAY_WEBHOOK_SECRET=test_webhook_secret
      - FIREBASE_AUTH_EMULATOR_HOST=localhost:9099
    volumes:
      - ./application:/application
      - ./reports:/application/reports
      - ./htmlcov:/application/htmlcov
    depends_on:
      test-db:
        condition: service_healthy
      test-redis:
        condition: service_healthy
      mock-services:
        condition: service_healthy
    networks:
      - test-network
    command: >
      sh -c "
        echo 'Waiting for services to be ready...' &&
        sleep 5 &&
        echo 'Running database migrations...' &&
        alembic upgrade head &&
        echo 'Starting test suite...' &&
        pytest --maxfail=5 --tb=short
      "

  # Firebase Auth Emulator (for integration tests)
  firebase-emulator:
    image: node:18-alpine
    container_name: oms-firebase-emulator
    ports:
      - "9099:9099"
      - "4000:4000"
    volumes:
      - ./tests/firebase:/app
    working_dir: /app
    command: >
      sh -c "
        npm install -g firebase-tools &&
        firebase emulators:start --only auth --project test-project
      "
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9099/"]
      interval: 10s
      timeout: 5s
      retries: 3
    networks:
      - test-network

volumes:
  test-db-data:

networks:
  test-network:
    driver: bridge
