"""
Performance tests for Rozana OMS service using Locust.

This module provides load testing scenarios for:
- Order creation endpoints
- Order retrieval endpoints
- Authentication flows
- Payment processing
"""

from locust import HttpUser, task, between
import json
import random
import string
from datetime import datetime


class OMSUser(HttpUser):
    """Base user class for OMS performance testing."""
    
    wait_time = between(1, 3)  # Wait 1-3 seconds between requests
    
    def on_start(self):
        """Set up user session."""
        self.auth_token = "test_auth_token_123"
        self.headers = {
            "authorization": self.auth_token,
            "Content-Type": "application/json"
        }
        self.customer_id = f"PERF-{random.randint(1000, 9999)}"
        self.facility_id = f"FAC-{random.randint(100, 999)}"

    def generate_order_data(self):
        """Generate random order data for testing."""
        return {
            "customer_id": self.customer_id,
            "customer_name": f"Performance Test User {random.randint(1, 1000)}",
            "facility_id": self.facility_id,
            "facility_name": f"Test Facility {random.randint(1, 100)}",
            "total_amount": round(random.uniform(50.0, 500.0), 2),
            "payment_mode": random.choice(["cod", "razorpay", "online"]),
            "items": [
                {
                    "sku": f"ITEM-{random.randint(1000, 9999)}",
                    "quantity": random.randint(1, 5),
                    "unit_price": round(random.uniform(10.0, 100.0), 2),
                    "sale_price": round(random.uniform(15.0, 120.0), 2),
                    "product_name": f"Test Product {random.randint(1, 1000)}"
                }
                for _ in range(random.randint(1, 3))
            ],
            "address": {
                "full_name": f"Test Customer {random.randint(1, 1000)}",
                "phone_number": f"+91{random.randint(1000000000, 9999999999)}",
                "address_line1": f"{random.randint(1, 999)} Test Street",
                "city": "Test City",
                "state": "Test State",
                "postal_code": f"{random.randint(100000, 999999)}",
                "country": "india",
                "type_of_address": random.choice(["home", "work", "other"])
            }
        }


class AppUser(OMSUser):
    """Mobile app user simulation."""
    
    weight = 3  # 3x more likely to be chosen than other user types
    
    @task(3)
    def create_order(self):
        """Test order creation via mobile app."""
        order_data = self.generate_order_data()
        
        with self.client.post(
            "/app/v1/create_order",
            json=order_data,
            headers=self.headers,
            catch_response=True
        ) as response:
            if response.status_code == 200:
                response.success()
                # Store order ID for later use
                if hasattr(response, 'json') and 'order_id' in response.json():
                    self.order_id = response.json()['order_id']
            else:
                response.failure(f"Order creation failed: {response.status_code}")

    @task(2)
    def get_order_details(self):
        """Test order details retrieval."""
        if hasattr(self, 'order_id'):
            order_id = self.order_id
        else:
            order_id = f"TEST{random.randint(1000, 9999)}"
        
        with self.client.get(
            f"/app/v1/order_details?order_id={order_id}",
            headers=self.headers,
            catch_response=True
        ) as response:
            if response.status_code in [200, 404]:  # 404 is acceptable for non-existent orders
                response.success()
            else:
                response.failure(f"Get order details failed: {response.status_code}")

    @task(1)
    def get_customer_orders(self):
        """Test customer orders retrieval."""
        with self.client.get(
            f"/app/v1/orders?customer_id={self.customer_id}",
            headers=self.headers,
            catch_response=True
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Get customer orders failed: {response.status_code}")

    @task(1)
    def create_payment_order(self):
        """Test payment order creation."""
        payment_data = {
            "order_id": getattr(self, 'order_id', f"TEST{random.randint(1000, 9999)}"),
            "amount": round(random.uniform(50.0, 500.0), 2),
            "currency": "INR"
        }
        
        with self.client.post(
            "/app/v1/create_payment_order",
            json=payment_data,
            headers=self.headers,
            catch_response=True
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Payment order creation failed: {response.status_code}")


class POSUser(OMSUser):
    """POS system user simulation."""
    
    weight = 2  # 2x weight
    
    @task(4)
    def create_pos_order(self):
        """Test order creation via POS system."""
        order_data = self.generate_order_data()
        order_data["payment_mode"] = "cash"  # POS typically uses cash
        
        with self.client.post(
            "/pos/v1/create_order",
            json=order_data,
            headers=self.headers,
            catch_response=True
        ) as response:
            if response.status_code == 200:
                response.success()
                if hasattr(response, 'json') and 'order_id' in response.json():
                    self.order_id = response.json()['order_id']
            else:
                response.failure(f"POS order creation failed: {response.status_code}")

    @task(2)
    def get_facility_orders(self):
        """Test facility orders retrieval."""
        with self.client.get(
            f"/pos/v1/facility_orders?facility_id={self.facility_id}",
            headers=self.headers,
            catch_response=True
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Get facility orders failed: {response.status_code}")

    @task(1)
    def update_order_status(self):
        """Test order status update."""
        if hasattr(self, 'order_id'):
            update_data = {
                "order_id": self.order_id,
                "status": random.choice([11, 12, 13])  # Various status codes
            }
            
            with self.client.put(
                "/pos/v1/update_order_status",
                json=update_data,
                headers=self.headers,
                catch_response=True
            ) as response:
                if response.status_code == 200:
                    response.success()
                else:
                    response.failure(f"Order status update failed: {response.status_code}")


class APIUser(OMSUser):
    """External API user simulation."""
    
    weight = 1  # Lower weight for API users
    
    def on_start(self):
        """Set up API user session."""
        super().on_start()
        self.api_token = "valid_api_token_123"
    
    @task(2)
    def get_orders_with_token(self):
        """Test order retrieval with token validation."""
        with self.client.get(
            f"/api/v1/orders?customer_id={self.customer_id}&token={self.api_token}",
            catch_response=True
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"API order retrieval failed: {response.status_code}")

    @task(1)
    def get_order_items_with_token(self):
        """Test order items retrieval with token validation."""
        order_id = getattr(self, 'order_id', f"TEST{random.randint(1000, 9999)}")
        
        with self.client.get(
            f"/api/v1/order_items?order_id={order_id}&token={self.api_token}",
            catch_response=True
        ) as response:
            if response.status_code in [200, 404]:
                response.success()
            else:
                response.failure(f"API order items retrieval failed: {response.status_code}")


class HealthCheckUser(HttpUser):
    """Health check user for basic monitoring."""
    
    weight = 1
    wait_time = between(5, 10)  # Less frequent health checks
    
    @task
    def health_check(self):
        """Test health check endpoint."""
        with self.client.get("/health", catch_response=True) as response:
            if response.status_code == 200:
                response.success()
                # Verify response structure
                try:
                    data = response.json()
                    if data.get("status") == "healthy":
                        response.success()
                    else:
                        response.failure("Health check returned unhealthy status")
                except:
                    response.failure("Health check response is not valid JSON")
            else:
                response.failure(f"Health check failed: {response.status_code}")


# Custom load testing scenarios
class StressTestUser(OMSUser):
    """High-load stress testing user."""
    
    weight = 1
    wait_time = between(0.1, 0.5)  # Very short wait times for stress testing
    
    @task
    def rapid_order_creation(self):
        """Rapid order creation for stress testing."""
        order_data = self.generate_order_data()
        
        with self.client.post(
            "/app/v1/create_order",
            json=order_data,
            headers=self.headers,
            catch_response=True
        ) as response:
            if response.status_code == 200:
                response.success()
            elif response.status_code == 429:  # Rate limiting
                response.success()  # Expected under high load
            else:
                response.failure(f"Stress test failed: {response.status_code}")


# Performance test configuration
class PerformanceTestConfig:
    """Configuration for different performance test scenarios."""
    
    # Light load: Normal usage simulation
    LIGHT_LOAD = {
        "users": 10,
        "spawn_rate": 2,
        "run_time": "2m"
    }
    
    # Medium load: Peak usage simulation
    MEDIUM_LOAD = {
        "users": 50,
        "spawn_rate": 5,
        "run_time": "5m"
    }
    
    # Heavy load: Stress testing
    HEAVY_LOAD = {
        "users": 100,
        "spawn_rate": 10,
        "run_time": "10m"
    }
    
    # Spike test: Sudden load increase
    SPIKE_TEST = {
        "users": 200,
        "spawn_rate": 50,
        "run_time": "3m"
    }


# Custom events for monitoring
from locust import events

@events.request.add_listener
def request_handler(request_type, name, response_time, response_length, exception, context, **kwargs):
    """Custom request handler for monitoring."""
    if exception:
        print(f"Request failed: {name} - {exception}")
    elif response_time > 2000:  # Log slow requests (>2s)
        print(f"Slow request detected: {name} - {response_time}ms")

@events.test_start.add_listener
def test_start_handler(environment, **kwargs):
    """Handler for test start event."""
    print("Performance test started")
    print(f"Target host: {environment.host}")

@events.test_stop.add_listener
def test_stop_handler(environment, **kwargs):
    """Handler for test stop event."""
    print("Performance test completed")
    print(f"Total requests: {environment.stats.total.num_requests}")
    print(f"Total failures: {environment.stats.total.num_failures}")
    print(f"Average response time: {environment.stats.total.avg_response_time}ms")
