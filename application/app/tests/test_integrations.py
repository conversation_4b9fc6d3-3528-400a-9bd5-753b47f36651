"""
Integration tests for external services.

This module tests:
- WMS (Warehouse Management System) integration
- Razorpay payment gateway integration
- Firebase authentication integration
- External API integrations
- End-to-end workflows
"""

import pytest
import json
from unittest.mock import patch, Mock, AsyncMock
from decimal import Decimal
from datetime import datetime, timedelta

from app.integrations.wms_service import wms_service
from app.integrations.razorpay_service import razorpay_service
from app.integrations.razorpay_config import razorpay_config
from app.core.constants import OrderStatus, PaymentStatus


class TestWMSIntegration:
    """Test WMS (Warehouse Management System) integration."""

    @pytest.mark.integration
    @pytest.mark.wms
    @pytest.mark.asyncio
    async def test_wms_authentication(self):
        """Test WMS authentication and token management."""
        with patch('httpx.AsyncClient') as mock_client:
            # Mock successful authentication response
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "access_token": "wms_token_123",
                "expires_in": 36000,
                "token_type": "Bearer"
            }
            
            mock_client.return_value.__aenter__.return_value.post = AsyncMock(return_value=mock_response)
            
            token = await wms_service.get_access_token()
            
            assert token == "wms_token_123"

    @pytest.mark.integration
    @pytest.mark.wms
    @pytest.mark.asyncio
    async def test_wms_create_outbound_order(self):
        """Test creating outbound order in WMS."""
        order_data = {
            "order_id": "TEST001",
            "customer_id": "CUST-001",
            "facility_id": "FAC-001",
            "items": [
                {
                    "sku": "ITEM-001",
                    "quantity": 2,
                    "unit_price": 25.00
                }
            ]
        }
        
        with patch('httpx.AsyncClient') as mock_client:
            # Mock WMS order creation response
            mock_response = Mock()
            mock_response.status_code = 201
            mock_response.json.return_value = {
                "success": True,
                "wms_order_id": "WMS-001",
                "status": "created"
            }
            
            mock_client.return_value.__aenter__.return_value.post = AsyncMock(return_value=mock_response)
            
            result = await wms_service.create_outbound_order(order_data)
            
            assert result["success"] is True
            assert result["wms_order_id"] == "WMS-001"

    @pytest.mark.integration
    @pytest.mark.wms
    @pytest.mark.asyncio
    async def test_wms_sync_order_background_task(self, mock_wms_service):
        """Test WMS order sync as background task."""
        order_data = {
            "order_id": "TEST001",
            "customer_id": "CUST-001",
            "items": []
        }
        
        mock_order_service = Mock()
        mock_order_service.update_order_status = AsyncMock(return_value={"success": True})
        
        # Test successful sync
        mock_wms_service.sync_order_to_wms.return_value = {"success": True}
        
        result = await wms_service.sync_order_to_wms(order_data, mock_order_service)
        
        assert result["success"] is True
        mock_order_service.update_order_status.assert_called()

    @pytest.mark.integration
    @pytest.mark.wms
    @pytest.mark.asyncio
    async def test_wms_cancel_order(self):
        """Test cancelling order in WMS."""
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "success": True,
                "status": "cancelled"
            }
            
            mock_client.return_value.__aenter__.return_value.put = AsyncMock(return_value=mock_response)
            
            result = await wms_service.cancel_outbound_order("WMS-001")
            
            assert result["success"] is True
            assert result["status"] == "cancelled"

    @pytest.mark.integration
    @pytest.mark.wms
    @pytest.mark.asyncio
    async def test_wms_error_handling(self):
        """Test WMS error handling."""
        with patch('httpx.AsyncClient') as mock_client:
            # Mock WMS error response
            mock_response = Mock()
            mock_response.status_code = 400
            mock_response.json.return_value = {
                "error": "Invalid order data",
                "code": "INVALID_DATA"
            }
            
            mock_client.return_value.__aenter__.return_value.post = AsyncMock(return_value=mock_response)
            
            result = await wms_service.create_outbound_order({})
            
            assert result["success"] is False
            assert "error" in result


class TestRazorpayIntegration:
    """Test Razorpay payment gateway integration."""

    @pytest.mark.integration
    @pytest.mark.razorpay
    def test_razorpay_config_initialization(self):
        """Test Razorpay configuration initialization."""
        assert razorpay_config.key_id == "test_key_id"
        assert razorpay_config.key_secret == "test_key_secret"
        assert razorpay_config.webhook_secret == "test_webhook_secret"
        assert razorpay_config.currency == "INR"

    @pytest.mark.integration
    @pytest.mark.razorpay
    @patch('razorpay.Client')
    def test_razorpay_create_order(self, mock_razorpay_client):
        """Test creating Razorpay order."""
        # Mock Razorpay client
        mock_client = Mock()
        mock_razorpay_client.return_value = mock_client
        
        mock_client.order.create.return_value = {
            "id": "order_test123",
            "amount": 9999,
            "currency": "INR",
            "status": "created"
        }
        
        order_data = {
            "amount": 99.99,
            "currency": "INR",
            "receipt": "TEST001"
        }
        
        result = razorpay_service.create_order(order_data)
        
        assert result["id"] == "order_test123"
        assert result["amount"] == 9999  # Amount in paise
        assert result["currency"] == "INR"

    @pytest.mark.integration
    @pytest.mark.razorpay
    @patch('razorpay.Client')
    def test_razorpay_verify_payment(self, mock_razorpay_client):
        """Test Razorpay payment verification."""
        mock_client = Mock()
        mock_razorpay_client.return_value = mock_client
        
        # Mock successful verification
        mock_client.utility.verify_payment_signature.return_value = True
        
        payment_data = {
            "razorpay_order_id": "order_test123",
            "razorpay_payment_id": "pay_test456",
            "razorpay_signature": "signature_test789"
        }
        
        result = razorpay_service.verify_payment(payment_data)
        
        assert result["success"] is True
        assert result["verified"] is True

    @pytest.mark.integration
    @pytest.mark.razorpay
    @patch('razorpay.Client')
    def test_razorpay_verify_payment_invalid_signature(self, mock_razorpay_client):
        """Test Razorpay payment verification with invalid signature."""
        mock_client = Mock()
        mock_razorpay_client.return_value = mock_client
        
        # Mock failed verification
        mock_client.utility.verify_payment_signature.return_value = False
        
        payment_data = {
            "razorpay_order_id": "order_test123",
            "razorpay_payment_id": "pay_test456",
            "razorpay_signature": "invalid_signature"
        }
        
        result = razorpay_service.verify_payment(payment_data)
        
        assert result["success"] is False
        assert result["verified"] is False

    @pytest.mark.integration
    @pytest.mark.razorpay
    @patch('razorpay.Client')
    def test_razorpay_webhook_verification(self, mock_razorpay_client):
        """Test Razorpay webhook signature verification."""
        mock_client = Mock()
        mock_razorpay_client.return_value = mock_client
        
        mock_client.utility.verify_webhook_signature.return_value = True
        
        webhook_body = '{"event": "payment.captured"}'
        webhook_signature = "test_signature"
        
        result = razorpay_service.verify_webhook_signature(webhook_body, webhook_signature)
        
        assert result is True

    @pytest.mark.integration
    @pytest.mark.razorpay
    @patch('razorpay.Client')
    def test_razorpay_refund_payment(self, mock_razorpay_client):
        """Test Razorpay payment refund."""
        mock_client = Mock()
        mock_razorpay_client.return_value = mock_client
        
        mock_client.payment.refund.return_value = {
            "id": "rfnd_test123",
            "amount": 5000,
            "currency": "INR",
            "status": "processed"
        }
        
        refund_data = {
            "payment_id": "pay_test456",
            "amount": 50.00,
            "notes": {"reason": "Customer request"}
        }
        
        result = razorpay_service.refund_payment(refund_data)
        
        assert result["id"] == "rfnd_test123"
        assert result["amount"] == 5000  # Amount in paise
        assert result["status"] == "processed"


class TestFirebaseIntegration:
    """Test Firebase authentication integration."""

    @pytest.mark.integration
    @pytest.mark.firebase
    @patch('firebase_admin.auth.verify_id_token')
    def test_firebase_token_verification_success(self, mock_verify):
        """Test successful Firebase token verification."""
        mock_verify.return_value = {
            "user_id": "firebase_user_123",
            "phone_number": "+1234567890",
            "email": "<EMAIL>",
            "exp": int((datetime.utcnow() + timedelta(hours=1)).timestamp())
        }
        
        from firebase_admin import auth
        
        token = "valid_firebase_token"
        result = auth.verify_id_token(token)
        
        assert result["user_id"] == "firebase_user_123"
        assert result["phone_number"] == "+1234567890"
        assert result["email"] == "<EMAIL>"

    @pytest.mark.integration
    @pytest.mark.firebase
    @patch('firebase_admin.auth.verify_id_token')
    def test_firebase_token_verification_expired(self, mock_verify):
        """Test Firebase token verification with expired token."""
        from firebase_admin.auth import ExpiredIdTokenError
        
        mock_verify.side_effect = ExpiredIdTokenError("Token expired")
        
        from firebase_admin import auth
        
        with pytest.raises(ExpiredIdTokenError):
            auth.verify_id_token("expired_token")

    @pytest.mark.integration
    @pytest.mark.firebase
    @patch('firebase_admin.auth.verify_id_token')
    def test_firebase_token_verification_invalid(self, mock_verify):
        """Test Firebase token verification with invalid token."""
        from firebase_admin.auth import InvalidIdTokenError
        
        mock_verify.side_effect = InvalidIdTokenError("Invalid token")
        
        from firebase_admin import auth
        
        with pytest.raises(InvalidIdTokenError):
            auth.verify_id_token("invalid_token")

    @pytest.mark.integration
    @pytest.mark.firebase
    @patch('firebase_admin.get_app')
    def test_firebase_app_initialization(self, mock_get_app):
        """Test Firebase app initialization."""
        mock_app = Mock()
        mock_get_app.return_value = mock_app
        
        from firebase_admin import get_app
        
        app_instance = get_app("app")
        pos_instance = get_app("pos")
        
        assert app_instance == mock_app
        assert pos_instance == mock_app


class TestEndToEndWorkflows:
    """Test complete end-to-end workflows."""

    @pytest.mark.integration
    @pytest.mark.slow
    @patch('firebase_admin.auth.verify_id_token')
    @patch('app.integrations.wms_service.wms_service')
    @patch('app.integrations.razorpay_service.razorpay_service')
    async def test_complete_order_workflow_with_payment(
        self, 
        mock_razorpay, 
        mock_wms, 
        mock_firebase, 
        async_client, 
        sample_order_data
    ):
        """Test complete order workflow from creation to payment."""
        # Setup mocks
        mock_firebase.return_value = {'user_id': 'test_user', 'phone_number': '+1234567890'}
        mock_wms.sync_order_to_wms = AsyncMock(return_value={"success": True})
        mock_razorpay.create_order.return_value = {
            "id": "order_test123",
            "amount": 9999,
            "currency": "INR",
            "status": "created"
        }
        mock_razorpay.verify_payment.return_value = {"success": True, "verified": True}
        
        headers = {"authorization": "valid_token"}
        
        # Step 1: Create order
        order_response = await async_client.post(
            "/app/v1/create_order", 
            json=sample_order_data, 
            headers=headers
        )
        assert order_response.status_code == 200
        order_id = order_response.json()["order_id"]
        
        # Step 2: Create payment order
        payment_data = {
            "order_id": order_id,
            "amount": 99.99,
            "currency": "INR"
        }
        payment_response = await async_client.post(
            "/app/v1/create_payment_order",
            json=payment_data,
            headers=headers
        )
        assert payment_response.status_code == 200
        
        # Step 3: Verify payment
        verification_data = {
            "razorpay_order_id": "order_test123",
            "razorpay_payment_id": "pay_test456",
            "razorpay_signature": "signature_test789"
        }
        verify_response = await async_client.post(
            "/app/v1/verify_payment",
            json=verification_data,
            headers=headers
        )
        assert verify_response.status_code == 200
        
        # Step 4: Check order status
        status_response = await async_client.get(
            f"/app/v1/order_details?order_id={order_id}",
            headers=headers
        )
        assert status_response.status_code == 200

    @pytest.mark.integration
    @pytest.mark.slow
    @patch('firebase_admin.auth.verify_id_token')
    @patch('app.integrations.wms_service.wms_service')
    async def test_order_cancellation_workflow(
        self, 
        mock_wms, 
        mock_firebase, 
        async_client, 
        sample_order_data
    ):
        """Test order cancellation workflow."""
        # Setup mocks
        mock_firebase.return_value = {'user_id': 'test_user', 'phone_number': '+1234567890'}
        mock_wms.sync_order_to_wms = AsyncMock(return_value={"success": True})
        mock_wms.cancel_outbound_order = AsyncMock(return_value={"success": True})
        
        headers = {"authorization": "valid_token"}
        
        # Step 1: Create order
        order_response = await async_client.post(
            "/app/v1/create_order", 
            json=sample_order_data, 
            headers=headers
        )
        assert order_response.status_code == 200
        order_id = order_response.json()["order_id"]
        
        # Step 2: Cancel order
        cancel_data = {
            "order_id": order_id,
            "customer_id": sample_order_data["customer_id"],
            "reason": "Customer request"
        }
        cancel_response = await async_client.post(
            "/app/v1/cancel_order",
            json=cancel_data,
            headers=headers
        )
        assert cancel_response.status_code == 200
        
        # Step 3: Verify cancellation
        status_response = await async_client.get(
            f"/app/v1/order_details?order_id={order_id}",
            headers=headers
        )
        assert status_response.status_code == 200
        # Order should be cancelled
        order_data = status_response.json()
        assert order_data["status"] == OrderStatus.CANCELLED

    @pytest.mark.integration
    @pytest.mark.external
    async def test_external_service_resilience(self):
        """Test system resilience when external services are down."""
        with patch('httpx.AsyncClient') as mock_client:
            # Mock network timeout
            mock_client.return_value.__aenter__.return_value.post = AsyncMock(
                side_effect=Exception("Connection timeout")
            )
            
            # WMS service should handle errors gracefully
            result = await wms_service.create_outbound_order({})
            assert result["success"] is False
            assert "error" in result
            
            # Razorpay service should handle errors gracefully
            with patch('razorpay.Client') as mock_razorpay:
                mock_razorpay.side_effect = Exception("Service unavailable")
                
                # Should not crash the application
                try:
                    razorpay_service.create_order({"amount": 100})
                except Exception as e:
                    assert "Service unavailable" in str(e)
