"""
Tests for core service classes.

This module tests:
- OrderService (order creation, updates, cancellation)
- OrderQueryService (order retrieval, filtering)
- PaymentService (payment management)
- Other core business logic services
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
from decimal import Decimal
from datetime import datetime, timedelta

from app.services.order_service import OrderService
from app.services.order_query_service import OrderQueryService
from app.services.payment_service import PaymentService
from app.core.constants import OrderStatus, PaymentStatus
from app.models.orders import Order, OrderItem
from app.models.payments import PaymentDetails


class TestOrderService:
    """Test OrderService for order creation and management."""

    @pytest.fixture
    def order_service(self):
        """Create OrderService instance."""
        return OrderService()

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_get_initial_status_app_online_payment(self, order_service):
        """Test initial status for app orders with online payment."""
        status = await order_service.get_initial_status("app", "razorpay")
        assert status == OrderStatus.DRAFT

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_get_initial_status_app_cod_payment(self, order_service):
        """Test initial status for app orders with COD payment."""
        status = await order_service.get_initial_status("app", "cod")
        assert status == OrderStatus.OPEN

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_get_initial_status_pos_payment(self, order_service):
        """Test initial status for POS orders."""
        status = await order_service.get_initial_status("pos", "cash")
        assert status == OrderStatus.OPEN

    @pytest.mark.unit
    @pytest.mark.asyncio
    @patch('app.connections.database.execute_raw_sql')
    async def test_create_order_success(self, mock_execute, order_service, sample_order_data):
        """Test successful order creation."""
        # Mock database response
        mock_execute.return_value = [{"order_id": "TEST001", "id": 1}]
        
        result = await order_service.create_order(sample_order_data, origin="app")
        
        assert result["order_id"] == "TEST001"
        assert result["status"] == "success"
        mock_execute.assert_called()

    @pytest.mark.unit
    @pytest.mark.asyncio
    @patch('app.connections.database.execute_raw_sql')
    async def test_create_order_with_background_tasks(self, mock_execute, order_service, sample_order_data):
        """Test order creation with background tasks."""
        mock_execute.return_value = [{"order_id": "TEST001", "id": 1}]
        mock_background_tasks = Mock()
        
        with patch('app.integrations.wms_service.wms_service') as mock_wms:
            result = await order_service.create_order(
                sample_order_data, 
                background_tasks=mock_background_tasks,
                origin="app"
            )
            
            assert result["order_id"] == "TEST001"
            # Background task should be added for WMS sync
            mock_background_tasks.add_task.assert_called()

    @pytest.mark.unit
    @pytest.mark.asyncio
    @patch('app.connections.database.execute_raw_sql')
    async def test_update_order_status(self, mock_execute, order_service):
        """Test order status update."""
        mock_execute.return_value = [{"success": True}]
        
        result = await order_service.update_order_status("TEST001", OrderStatus.CONFIRMED)
        
        assert result["success"] is True
        mock_execute.assert_called()

    @pytest.mark.unit
    @pytest.mark.asyncio
    @patch('app.connections.database.execute_raw_sql')
    async def test_cancel_order(self, mock_execute, order_service):
        """Test order cancellation."""
        mock_execute.return_value = [{"success": True}]
        
        result = await order_service.cancel_order("TEST001", "Customer request")
        
        assert result["success"] is True
        mock_execute.assert_called()


class TestOrderQueryService:
    """Test OrderQueryService for order retrieval operations."""

    @pytest.fixture
    def query_service(self):
        """Create OrderQueryService instance."""
        return OrderQueryService()

    @pytest.mark.unit
    @patch('app.connections.database.execute_raw_sql_readonly')
    def test_get_order_by_id_found(self, mock_execute, query_service):
        """Test retrieving existing order by ID."""
        mock_order_data = {
            "order_id": "TEST001",
            "customer_id": "CUST-001",
            "customer_name": "Test Customer",
            "status": OrderStatus.OPEN,
            "total_amount": Decimal("99.99")
        }
        mock_execute.return_value = [mock_order_data]
        
        result = query_service.get_order_by_id("TEST001")
        
        assert result is not None
        assert result["order_id"] == "TEST001"
        assert result["customer_id"] == "CUST-001"

    @pytest.mark.unit
    @patch('app.connections.database.execute_raw_sql_readonly')
    def test_get_order_by_id_not_found(self, mock_execute, query_service):
        """Test retrieving non-existent order by ID."""
        mock_execute.return_value = []
        
        result = query_service.get_order_by_id("NONEXISTENT")
        
        assert result is None

    @pytest.mark.unit
    @patch('app.connections.database.execute_raw_sql_readonly')
    def test_get_orders_by_customer(self, mock_execute, query_service):
        """Test retrieving orders by customer ID."""
        mock_orders = [
            {"order_id": "TEST001", "customer_id": "CUST-001"},
            {"order_id": "TEST002", "customer_id": "CUST-001"}
        ]
        mock_execute.return_value = mock_orders
        
        result = query_service.get_orders_by_customer("CUST-001")
        
        assert len(result) == 2
        assert all(order["customer_id"] == "CUST-001" for order in result)

    @pytest.mark.unit
    @patch('app.connections.database.execute_raw_sql_readonly')
    def test_get_orders_by_facility(self, mock_execute, query_service):
        """Test retrieving orders by facility ID."""
        mock_orders = [
            {"order_id": "TEST001", "facility_id": "FAC-001"},
            {"order_id": "TEST002", "facility_id": "FAC-001"}
        ]
        mock_execute.return_value = mock_orders
        
        result = query_service.get_orders_by_facility("FAC-001")
        
        assert len(result) == 2
        assert all(order["facility_id"] == "FAC-001" for order in result)

    @pytest.mark.unit
    @patch('app.connections.database.execute_raw_sql_readonly')
    def test_get_orders_with_status_filter(self, mock_execute, query_service):
        """Test retrieving orders with status filter."""
        mock_orders = [
            {"order_id": "TEST001", "status": OrderStatus.OPEN},
            {"order_id": "TEST002", "status": OrderStatus.OPEN}
        ]
        mock_execute.return_value = mock_orders
        
        result = query_service.get_orders_with_filters(status=OrderStatus.OPEN)
        
        assert len(result) == 2
        assert all(order["status"] == OrderStatus.OPEN for order in result)


class TestPaymentService:
    """Test PaymentService for payment management."""

    @pytest.fixture
    def payment_service(self, db_session):
        """Create PaymentService instance with database session."""
        return PaymentService(db_session)

    @pytest.mark.unit
    def test_create_payment_record(self, payment_service, db_session):
        """Test creating a payment record."""
        payment_data = {
            "order_id": "TEST001",
            "amount": Decimal("99.99"),
            "currency": "INR",
            "payment_method": "razorpay"
        }
        
        result = payment_service.create_payment_record(**payment_data)
        
        assert result is not None
        assert result.order_id == "TEST001"
        assert result.amount == Decimal("99.99")
        assert result.status == PaymentStatus.PENDING

    @pytest.mark.unit
    def test_update_payment_status(self, payment_service, db_session):
        """Test updating payment status."""
        # Create a payment first
        payment = create_test_payment(db_session, "TEST001")
        
        result = payment_service.update_payment_status(
            payment.payment_id, 
            PaymentStatus.COMPLETED,
            transaction_id="txn_123"
        )
        
        assert result is not None
        assert result.status == PaymentStatus.COMPLETED
        assert result.transaction_id == "txn_123"

    @pytest.mark.unit
    def test_get_payment_by_order_id(self, payment_service, db_session):
        """Test retrieving payment by order ID."""
        # Create a payment first
        payment = create_test_payment(db_session, "TEST001")
        
        result = payment_service.get_payment_by_order_id("TEST001")
        
        assert result is not None
        assert result.order_id == "TEST001"
        assert result.payment_id == payment.payment_id

    @pytest.mark.unit
    def test_get_payment_by_payment_id(self, payment_service, db_session):
        """Test retrieving payment by payment ID."""
        # Create a payment first
        payment = create_test_payment(db_session, "TEST001")
        
        result = payment_service.get_payment_by_payment_id(payment.payment_id)
        
        assert result is not None
        assert result.payment_id == payment.payment_id
        assert result.order_id == "TEST001"

    @pytest.mark.unit
    def test_get_payments_by_status(self, payment_service, db_session):
        """Test retrieving payments by status."""
        # Create multiple payments
        create_test_payment(db_session, "TEST001", status=PaymentStatus.PENDING)
        create_test_payment(db_session, "TEST002", status=PaymentStatus.COMPLETED)
        create_test_payment(db_session, "TEST003", status=PaymentStatus.PENDING)
        
        result = payment_service.get_payments_by_status(PaymentStatus.PENDING)
        
        assert len(result) == 2
        assert all(payment.status == PaymentStatus.PENDING for payment in result)

    @pytest.mark.unit
    def test_calculate_payment_totals(self, payment_service, db_session):
        """Test calculating payment totals."""
        # Create payments with different amounts
        create_test_payment(db_session, "TEST001", amount=Decimal("50.00"), status=PaymentStatus.COMPLETED)
        create_test_payment(db_session, "TEST002", amount=Decimal("75.00"), status=PaymentStatus.COMPLETED)
        create_test_payment(db_session, "TEST003", amount=Decimal("25.00"), status=PaymentStatus.PENDING)
        
        completed_total = payment_service.get_total_by_status(PaymentStatus.COMPLETED)
        pending_total = payment_service.get_total_by_status(PaymentStatus.PENDING)
        
        assert completed_total == Decimal("125.00")
        assert pending_total == Decimal("25.00")

    @pytest.mark.unit
    def test_payment_refund(self, payment_service, db_session):
        """Test payment refund functionality."""
        # Create a completed payment
        payment = create_test_payment(
            db_session, 
            "TEST001", 
            status=PaymentStatus.COMPLETED,
            amount=Decimal("99.99")
        )
        
        result = payment_service.process_refund(
            payment.payment_id,
            Decimal("50.00"),
            "Partial refund"
        )
        
        assert result is not None
        assert result.status == PaymentStatus.PARTIALLY_REFUNDED
        assert result.refunded_amount == Decimal("50.00")

    @pytest.mark.unit
    def test_payment_validation(self, payment_service):
        """Test payment data validation."""
        # Test invalid amount
        with pytest.raises(ValueError):
            payment_service.create_payment_record(
                order_id="TEST001",
                amount=Decimal("-10.00"),  # Negative amount
                currency="INR",
                payment_method="razorpay"
            )
        
        # Test invalid currency
        with pytest.raises(ValueError):
            payment_service.create_payment_record(
                order_id="TEST001",
                amount=Decimal("99.99"),
                currency="INVALID",  # Invalid currency
                payment_method="razorpay"
            )


# Helper function for creating test payments (imported from conftest.py)
def create_test_payment(db_session, order_id: str, **kwargs):
    """Create a test payment in the database."""
    from app.tests.conftest import create_test_payment as _create_test_payment
    return _create_test_payment(db_session, order_id, **kwargs)
