"""
Authentication tests for Firebase middleware and external authentication endpoints.

This module tests:
- Firebase authentication middleware for app and POS routes
- External Firebase authentication endpoints (token refresh, sign-in)
- Token validation and user state management
"""

import pytest
import httpx
import json
from unittest.mock import patch, Mock, AsyncMock
from fastapi import HTT<PERSON>Exception
from fastapi.testclient import TestClient

from app.middlewares.firebase_auth_app import FirebaseAuthMiddlewareAPP
from app.middlewares.firebase_auth_pos import FirebaseAuthMiddlewarePOS
from app.middlewares.token_validation import TokenValidationService


class TestFirebaseAuthMiddlewareAPP:
    """Test Firebase authentication middleware for app routes."""

    @pytest.mark.auth
    def test_middleware_allows_options_requests(self, client):
        """Test that OPTIONS requests bypass authentication."""
        response = client.options("/app/v1/create_order")
        # Should not return 401 for OPTIONS
        assert response.status_code != 401

    @pytest.mark.auth
    def test_middleware_allows_non_app_routes(self, client):
        """Test that non-app routes bypass app authentication."""
        response = client.get("/health")
        assert response.status_code == 200

    @pytest.mark.auth
    def test_middleware_rejects_missing_auth_header(self, client):
        """Test that requests without authorization header are rejected."""
        response = client.post("/app/v1/create_order", json={})
        assert response.status_code == 401
        assert response.json()["detail"] == "Unauthorized"

    @pytest.mark.auth
    @patch('firebase_admin.auth.verify_id_token')
    def test_middleware_accepts_valid_token(self, mock_verify, client, sample_order_data):
        """Test that valid Firebase tokens are accepted."""
        mock_verify.return_value = {
            'user_id': 'test_user_123',
            'phone_number': '+**********'
        }
        
        headers = {"authorization": "valid_token"}
        response = client.post("/app/v1/create_order", json=sample_order_data, headers=headers)
        
        # Should not be 401 (may be other errors due to missing data)
        assert response.status_code != 401

    @pytest.mark.auth
    @patch('firebase_admin.auth.verify_id_token')
    def test_middleware_rejects_invalid_token(self, mock_verify, client):
        """Test that invalid Firebase tokens are rejected."""
        mock_verify.side_effect = Exception("Invalid token")
        
        headers = {"authorization": "invalid_token"}
        response = client.post("/app/v1/create_order", json={}, headers=headers)
        
        assert response.status_code == 401
        assert response.json()["detail"] == "Invalid token"


class TestFirebaseAuthMiddlewarePOS:
    """Test Firebase authentication middleware for POS routes."""

    @pytest.mark.auth
    def test_middleware_allows_options_requests(self, client):
        """Test that OPTIONS requests bypass authentication."""
        response = client.options("/pos/v1/create_order")
        assert response.status_code != 401

    @pytest.mark.auth
    def test_middleware_allows_non_pos_routes(self, client):
        """Test that non-POS routes bypass POS authentication."""
        response = client.get("/health")
        assert response.status_code == 200

    @pytest.mark.auth
    def test_middleware_rejects_missing_auth_header(self, client):
        """Test that requests without authorization header are rejected."""
        response = client.post("/pos/v1/create_order", json={})
        assert response.status_code == 401
        assert response.json()["detail"] == "Unauthorized"

    @pytest.mark.auth
    @patch('firebase_admin.auth.verify_id_token')
    def test_middleware_accepts_valid_token(self, mock_verify, client, sample_order_data):
        """Test that valid Firebase tokens are accepted."""
        mock_verify.return_value = {
            'user_id': 'pos_user_123',
            'phone_number': '+**********'
        }
        
        headers = {"authorization": "valid_pos_token"}
        response = client.post("/pos/v1/create_order", json=sample_order_data, headers=headers)
        
        # Should not be 401 (may be other errors due to missing data)
        assert response.status_code != 401

    @pytest.mark.auth
    @patch('firebase_admin.auth.verify_id_token')
    def test_middleware_rejects_invalid_token(self, mock_verify, client):
        """Test that invalid Firebase tokens are rejected."""
        mock_verify.side_effect = Exception("Invalid token")
        
        headers = {"authorization": "invalid_pos_token"}
        response = client.post("/pos/v1/create_order", json={}, headers=headers)
        
        assert response.status_code == 401
        assert response.json()["detail"] == "Invalid token"


class TestTokenValidationService:
    """Test token validation service for API routes."""

    @pytest.mark.auth
    @pytest.mark.asyncio
    async def test_validate_token_success(self):
        """Test successful token validation."""
        service = TokenValidationService("http://test-api.com/api/check-token/")
        
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {"valid": True}
            
            mock_client.return_value.__aenter__.return_value.get = AsyncMock(return_value=mock_response)
            
            result = await service.validate_token("valid_token")
            assert result is True

    @pytest.mark.auth
    @pytest.mark.asyncio
    async def test_validate_token_failure(self):
        """Test failed token validation."""
        service = TokenValidationService("http://test-api.com/api/check-token/")
        
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = Mock()
            mock_response.status_code = 401
            mock_response.text = "Unauthorized"
            
            mock_client.return_value.__aenter__.return_value.get = AsyncMock(return_value=mock_response)
            
            result = await service.validate_token("invalid_token")
            assert result is False

    @pytest.mark.auth
    @pytest.mark.asyncio
    async def test_validate_token_network_error(self):
        """Test token validation with network error."""
        service = TokenValidationService("http://test-api.com/api/check-token/")
        
        with patch('httpx.AsyncClient') as mock_client:
            mock_client.return_value.__aenter__.return_value.get = AsyncMock(
                side_effect=httpx.RequestError("Network error")
            )
            
            result = await service.validate_token("test_token")
            assert result is False


class TestExternalAuthenticationEndpoints:
    """Test external Firebase authentication endpoints."""

    @pytest.mark.external
    @pytest.mark.firebase
    @pytest.mark.asyncio
    async def test_firebase_token_refresh_endpoint(self):
        """Test Firebase token refresh endpoint integration."""
        # Mock the external Firebase token refresh endpoint
        refresh_url = "https://securetoken.googleapis.com/v1/token"
        api_key = "AIzaSyBEEWROSUbSLVl9T3HHSp8VdgsU6oTrdFI"
        
        mock_response_data = {
            "access_token": "new_access_token_123",
            "expires_in": "3600",
            "token_type": "Bearer",
            "refresh_token": "new_refresh_token_456",
            "id_token": "new_id_token_789",
            "user_id": "test_user_123",
            "project_id": "test-project"
        }
        
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = mock_response_data
            
            mock_client.return_value.__aenter__.return_value.post = AsyncMock(return_value=mock_response)
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{refresh_url}?key={api_key}",
                    headers={
                        'accept': '*/*',
                        'content-type': 'application/x-www-form-urlencoded',
                        'origin': 'https://web-dev.rozana.tech'
                    },
                    data={
                        'grant_type': 'refresh_token',
                        'refresh_token': 'test_refresh_token'
                    }
                )
                
                assert response.status_code == 200
                data = response.json()
                assert "access_token" in data
                assert "refresh_token" in data
                assert "id_token" in data

    @pytest.mark.external
    @pytest.mark.firebase
    @pytest.mark.asyncio
    async def test_google_identity_toolkit_signin_endpoint(self):
        """Test Google Identity Toolkit sign-in endpoint integration."""
        # Mock the external Google Identity Toolkit sign-in endpoint
        signin_url = "https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword"
        api_key = "AIzaSyC_fE1A0JapukDalgCdJix4mmoVWx-K-2g"
        
        mock_response_data = {
            "kind": "identitytoolkit#VerifyPasswordResponse",
            "localId": "test_user_123",
            "email": "<EMAIL>",
            "displayName": "Test User",
            "idToken": "test_id_token_123",
            "registered": True,
            "refreshToken": "test_refresh_token_456",
            "expiresIn": "3600"
        }
        
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = mock_response_data
            
            mock_client.return_value.__aenter__.return_value.post = AsyncMock(return_value=mock_response)
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{signin_url}?key={api_key}",
                    headers={'Content-Type': 'application/json'},
                    json={
                        "email": "<EMAIL>",
                        "password": "test_password",
                        "returnSecureToken": True
                    }
                )
                
                assert response.status_code == 200
                data = response.json()
                assert "idToken" in data
                assert "refreshToken" in data
                assert "localId" in data
                assert data["email"] == "<EMAIL>"

    @pytest.mark.external
    @pytest.mark.firebase
    @pytest.mark.asyncio
    async def test_firebase_token_refresh_invalid_token(self):
        """Test Firebase token refresh with invalid refresh token."""
        refresh_url = "https://securetoken.googleapis.com/v1/token"
        api_key = "AIzaSyBEEWROSUbSLVl9T3HHSp8VdgsU6oTrdFI"
        
        mock_error_response = {
            "error": {
                "code": 400,
                "message": "INVALID_REFRESH_TOKEN",
                "errors": [
                    {
                        "message": "INVALID_REFRESH_TOKEN",
                        "domain": "global",
                        "reason": "invalid"
                    }
                ]
            }
        }
        
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = Mock()
            mock_response.status_code = 400
            mock_response.json.return_value = mock_error_response
            
            mock_client.return_value.__aenter__.return_value.post = AsyncMock(return_value=mock_response)
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{refresh_url}?key={api_key}",
                    headers={'content-type': 'application/x-www-form-urlencoded'},
                    data={
                        'grant_type': 'refresh_token',
                        'refresh_token': 'invalid_refresh_token'
                    }
                )
                
                assert response.status_code == 400
                data = response.json()
                assert "error" in data
                assert data["error"]["message"] == "INVALID_REFRESH_TOKEN"

    @pytest.mark.external
    @pytest.mark.firebase
    @pytest.mark.asyncio
    async def test_google_identity_toolkit_signin_invalid_credentials(self):
        """Test Google Identity Toolkit sign-in with invalid credentials."""
        signin_url = "https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword"
        api_key = "AIzaSyC_fE1A0JapukDalgCdJix4mmoVWx-K-2g"
        
        mock_error_response = {
            "error": {
                "code": 400,
                "message": "INVALID_PASSWORD",
                "errors": [
                    {
                        "message": "INVALID_PASSWORD",
                        "domain": "global",
                        "reason": "invalid"
                    }
                ]
            }
        }
        
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = Mock()
            mock_response.status_code = 400
            mock_response.json.return_value = mock_error_response
            
            mock_client.return_value.__aenter__.return_value.post = AsyncMock(return_value=mock_response)
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{signin_url}?key={api_key}",
                    headers={'Content-Type': 'application/json'},
                    json={
                        "email": "<EMAIL>",
                        "password": "wrong_password",
                        "returnSecureToken": True
                    }
                )
                
                assert response.status_code == 400
                data = response.json()
                assert "error" in data
                assert data["error"]["message"] == "INVALID_PASSWORD"
