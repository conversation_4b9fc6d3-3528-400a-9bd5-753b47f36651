"""
Pytest configuration and fixtures for Rozana OMS service tests.

This module provides shared fixtures, mocks, and test configuration
for comprehensive testing of the OMS service.
"""

import os
import pytest
import asyncio
from typing import AsyncGenerator, Generator, Dict, Any
from unittest.mock import Mock, patch, AsyncMock
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
from fastapi.testclient import TestClient
import httpx
import json
from datetime import datetime, timedelta
import uuid

# Set testing environment variables before importing app modules
os.environ.update({
    "TESTING": "true",
    "DEBUG": "true",
    "DATABASE_URL": "postgresql://test_user:test_password@localhost:5433/test_oms_db",
    "REDIS_URL": "redis://localhost:6380/0",
    "STOCK_CHECK_ENABLED": "false",
    "WMS_INTEGRATION_ENABLED": "false",
    "RAZORPAY_INTEGRATION_ENABLED": "false",
    "SENTRY_ENABLED": "false",
    "OTEL_ENABLED": "false",
    "ALLOWED_ORIGINS": "*",
    "TOKEN_VALIDATION_URL": "http://localhost:8001",
    "RAZORPAY_KEY_ID": "test_key_id",
    "RAZORPAY_KEY_SECRET": "test_key_secret",
    "RAZORPAY_WEBHOOK_SECRET": "test_webhook_secret",
})

# Import app modules after setting environment
from app.main import app
from app.connections.database import Base, engine, get_db, get_read_db
from app.models.orders import Order, OrderItem, OrderAddress
from app.models.payments import PaymentDetails
from app.core.constants import OrderStatus, PaymentStatus


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
def test_database_url():
    """Test database URL."""
    return "postgresql://test_user:test_password@localhost:5433/test_oms_db"


@pytest.fixture(scope="session")
def test_engine(test_database_url):
    """Create test database engine."""
    engine = create_engine(
        test_database_url,
        pool_pre_ping=True,
        echo=False,
        connect_args={"options": "-c timezone=Asia/Kolkata"}
    )
    return engine


@pytest.fixture(scope="session")
def test_session_factory(test_engine):
    """Create test session factory."""
    return sessionmaker(autocommit=False, autoflush=False, bind=test_engine)


@pytest.fixture(scope="function")
async def db_session(test_session_factory):
    """Create a test database session with transaction rollback."""
    session = test_session_factory()
    
    # Start a transaction
    transaction = session.begin()
    
    try:
        yield session
    finally:
        # Rollback transaction to clean up
        transaction.rollback()
        session.close()


@pytest.fixture(scope="function")
def override_get_db(db_session):
    """Override the get_db dependency for testing."""
    def _override_get_db():
        yield db_session
    
    app.dependency_overrides[get_db] = _override_get_db
    app.dependency_overrides[get_read_db] = _override_get_db
    
    yield db_session
    
    # Clean up
    app.dependency_overrides.clear()


@pytest.fixture(scope="function")
def client(override_get_db):
    """Create a test client with database override."""
    with TestClient(app) as test_client:
        yield test_client


@pytest.fixture(scope="function")
async def async_client(override_get_db):
    """Create an async test client."""
    async with httpx.AsyncClient(app=app, base_url="http://test") as client:
        yield client


# Firebase Authentication Mocks
@pytest.fixture
def mock_firebase_app():
    """Mock Firebase app instance."""
    with patch('firebase_admin.get_app') as mock_get_app:
        mock_app = Mock()
        mock_get_app.return_value = mock_app
        yield mock_app


@pytest.fixture
def mock_firebase_auth():
    """Mock Firebase auth verification."""
    with patch('firebase_admin.auth.verify_id_token') as mock_verify:
        mock_verify.return_value = {
            'user_id': 'test_user_123',
            'phone_number': '+1234567890',
            'email': '<EMAIL>'
        }
        yield mock_verify


@pytest.fixture
def valid_firebase_token():
    """Valid Firebase token for testing."""
    return "valid_firebase_token_123"


@pytest.fixture
def invalid_firebase_token():
    """Invalid Firebase token for testing."""
    return "invalid_firebase_token_456"


# Test Data Fixtures
@pytest.fixture
def sample_order_data():
    """Sample order data for testing."""
    return {
        "customer_id": "CUST-001",
        "customer_name": "Test Customer",
        "facility_id": "FAC-001",
        "facility_name": "Test Facility",
        "total_amount": 99.99,
        "payment_mode": "cod",
        "items": [
            {
                "sku": "ITEM-001",
                "quantity": 2,
                "unit_price": 25.00,
                "sale_price": 29.99,
                "product_name": "Test Product 1"
            },
            {
                "sku": "ITEM-002",
                "quantity": 1,
                "unit_price": 40.00,
                "sale_price": 45.00,
                "product_name": "Test Product 2"
            }
        ],
        "address": {
            "full_name": "Test Customer",
            "phone_number": "+1234567890",
            "address_line1": "123 Test Street",
            "city": "Test City",
            "state": "Test State",
            "postal_code": "12345",
            "country": "india",
            "type_of_address": "home"
        }
    }


@pytest.fixture
def sample_payment_data():
    """Sample payment data for testing."""
    return {
        "order_id": "TEST001",
        "amount": 99.99,
        "currency": "INR",
        "payment_method": "razorpay"
    }


# External Service Mocks
@pytest.fixture
def mock_razorpay_client():
    """Mock Razorpay client."""
    with patch('app.integrations.razorpay_service.razorpay.Client') as mock_client:
        mock_instance = Mock()
        mock_client.return_value = mock_instance
        
        # Mock order creation
        mock_instance.order.create.return_value = {
            'id': 'order_test123',
            'amount': 9999,
            'currency': 'INR',
            'status': 'created'
        }
        
        # Mock payment verification
        mock_instance.utility.verify_payment_signature.return_value = True
        
        yield mock_instance


@pytest.fixture
def mock_wms_service():
    """Mock WMS service."""
    with patch('app.integrations.wms_service.wms_service') as mock_wms:
        mock_wms.sync_order_to_wms = AsyncMock(return_value={"success": True})
        mock_wms.create_outbound_order = AsyncMock(return_value={"success": True})
        mock_wms.cancel_outbound_order = AsyncMock(return_value={"success": True})
        yield mock_wms


@pytest.fixture
def mock_redis():
    """Mock Redis connection."""
    with patch('app.connections.redis.redis_client') as mock_redis:
        mock_redis.get = AsyncMock(return_value=None)
        mock_redis.set = AsyncMock(return_value=True)
        mock_redis.delete = AsyncMock(return_value=True)
        yield mock_redis


# Authentication Headers
@pytest.fixture
def auth_headers_app(valid_firebase_token):
    """Authentication headers for app routes."""
    return {"authorization": valid_firebase_token}


@pytest.fixture
def auth_headers_pos(valid_firebase_token):
    """Authentication headers for POS routes."""
    return {"authorization": valid_firebase_token}


# Database Setup and Teardown
@pytest.fixture(scope="session", autouse=True)
def setup_test_database(test_engine):
    """Set up test database schema."""
    # Create all tables
    Base.metadata.create_all(bind=test_engine)
    yield
    # Drop all tables after tests
    Base.metadata.drop_all(bind=test_engine)


# Utility Functions
def create_test_order(db_session: Session, **kwargs) -> Order:
    """Create a test order in the database."""
    order_data = {
        "random_prefix": "TEST",
        "customer_id": "CUST-001",
        "customer_name": "Test Customer",
        "facility_id": "FAC-001",
        "facility_name": "Test Facility",
        "status": OrderStatus.OPEN,
        "total_amount": 99.99,
        "order_mode": "app",
        **kwargs
    }
    
    order = Order(**order_data)
    db_session.add(order)
    db_session.commit()
    db_session.refresh(order)
    return order


def create_test_order_item(db_session: Session, order_id: int, **kwargs) -> OrderItem:
    """Create a test order item in the database."""
    item_data = {
        "order_id": order_id,
        "sku": "TEST-SKU",
        "quantity": 1,
        "unit_price": 25.00,
        "sale_price": 29.99,
        "product_name": "Test Product",
        "status": OrderStatus.OPEN,
        **kwargs
    }
    
    item = OrderItem(**item_data)
    db_session.add(item)
    db_session.commit()
    db_session.refresh(item)
    return item


def create_test_payment(db_session: Session, order_id: str, **kwargs) -> PaymentDetails:
    """Create a test payment in the database."""
    payment_data = {
        "order_id": order_id,
        "payment_id": f"pay_test_{uuid.uuid4().hex[:8]}",
        "amount": 99.99,
        "currency": "INR",
        "status": PaymentStatus.PENDING,
        "payment_method": "razorpay",
        **kwargs
    }
    
    payment = PaymentDetails(**payment_data)
    db_session.add(payment)
    db_session.commit()
    db_session.refresh(payment)
    return payment
