"""
API endpoint tests for all routes.

This module tests:
- Health check endpoints
- App routes (mobile app endpoints)
- POS routes (point of sale endpoints)
- Web routes (web interface endpoints)
- API routes (external API endpoints)
- Payment routes (Razorpay integration)
- Webhook routes (external webhooks)
"""

import pytest
import json
from unittest.mock import patch, Mock, AsyncMock
from fastapi import status
from decimal import Decimal

from app.core.constants import OrderStatus, PaymentStatus


class TestHealthEndpoints:
    """Test health check endpoints."""

    @pytest.mark.api
    def test_health_check(self, client):
        """Test basic health check endpoint."""
        response = client.get("/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert data["version"] == "4.0.0"
        assert data["service"] == "rozana-oms"


class TestAppRoutes:
    """Test mobile app routes."""

    @pytest.mark.api
    @patch('firebase_admin.auth.verify_id_token')
    @patch('app.core.order_functions.create_order_core')
    def test_create_order_app(self, mock_create_order, mock_verify, client, sample_order_data, auth_headers_app):
        """Test order creation via mobile app."""
        mock_verify.return_value = {'user_id': 'test_user', 'phone_number': '+**********'}
        mock_create_order.return_value = {
            "order_id": "TEST001",
            "status": "success",
            "message": "Order created successfully"
        }
        
        response = client.post("/app/v1/create_order", json=sample_order_data, headers=auth_headers_app)
        
        assert response.status_code == 200
        data = response.json()
        assert data["order_id"] == "TEST001"
        mock_create_order.assert_called_once()

    @pytest.mark.api
    @patch('firebase_admin.auth.verify_id_token')
    @patch('app.core.order_functions.get_order_details_core')
    def test_get_order_details_app(self, mock_get_order, mock_verify, client, auth_headers_app):
        """Test getting order details via mobile app."""
        mock_verify.return_value = {'user_id': 'test_user', 'phone_number': '+**********'}
        mock_get_order.return_value = {
            "order_id": "TEST001",
            "customer_id": "CUST-001",
            "status": OrderStatus.OPEN,
            "items": []
        }
        
        response = client.get("/app/v1/order_details?order_id=TEST001", headers=auth_headers_app)
        
        assert response.status_code == 200
        data = response.json()
        assert data["order_id"] == "TEST001"

    @pytest.mark.api
    @patch('firebase_admin.auth.verify_id_token')
    @patch('app.core.order_functions.get_all_orders_core')
    def test_get_all_orders_app(self, mock_get_orders, mock_verify, client, auth_headers_app):
        """Test getting all orders via mobile app."""
        mock_verify.return_value = {'user_id': 'test_user', 'phone_number': '+**********'}
        mock_get_orders.return_value = [
            {"order_id": "TEST001", "status": OrderStatus.OPEN},
            {"order_id": "TEST002", "status": OrderStatus.CONFIRMED}
        ]
        
        response = client.get("/app/v1/orders?customer_id=CUST-001", headers=auth_headers_app)
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2

    @pytest.mark.api
    @patch('firebase_admin.auth.verify_id_token')
    @patch('app.core.order_cancel.cancel_order_core')
    def test_cancel_order_app(self, mock_cancel, mock_verify, client, auth_headers_app):
        """Test order cancellation via mobile app."""
        mock_verify.return_value = {'user_id': 'test_user', 'phone_number': '+**********'}
        mock_cancel.return_value = {"success": True, "message": "Order cancelled"}
        
        cancel_data = {
            "order_id": "TEST001",
            "customer_id": "CUST-001",
            "reason": "Customer request"
        }
        
        response = client.post("/app/v1/cancel_order", json=cancel_data, headers=auth_headers_app)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True


class TestPOSRoutes:
    """Test POS (Point of Sale) routes."""

    @pytest.mark.api
    @patch('firebase_admin.auth.verify_id_token')
    @patch('app.core.order_functions.create_order_core')
    def test_create_order_pos(self, mock_create_order, mock_verify, client, sample_order_data, auth_headers_pos):
        """Test order creation via POS system."""
        mock_verify.return_value = {'user_id': 'pos_user', 'phone_number': '+**********'}
        mock_create_order.return_value = {
            "order_id": "POS001",
            "status": "success",
            "message": "Order created successfully"
        }
        
        response = client.post("/pos/v1/create_order", json=sample_order_data, headers=auth_headers_pos)
        
        assert response.status_code == 200
        data = response.json()
        assert data["order_id"] == "POS001"

    @pytest.mark.api
    @patch('firebase_admin.auth.verify_id_token')
    @patch('app.core.order_functions.get_all_facility_orders_core')
    def test_get_facility_orders_pos(self, mock_get_orders, mock_verify, client, auth_headers_pos):
        """Test getting facility orders via POS system."""
        mock_verify.return_value = {'user_id': 'pos_user', 'phone_number': '+**********'}
        mock_get_orders.return_value = [
            {"order_id": "POS001", "facility_id": "FAC-001"},
            {"order_id": "POS002", "facility_id": "FAC-001"}
        ]
        
        response = client.get("/pos/v1/facility_orders?facility_id=FAC-001", headers=auth_headers_pos)
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2

    @pytest.mark.api
    @patch('firebase_admin.auth.verify_id_token')
    @patch('app.core.order_updates.update_order_status_core')
    def test_update_order_status_pos(self, mock_update, mock_verify, client, auth_headers_pos):
        """Test order status update via POS system."""
        mock_verify.return_value = {'user_id': 'pos_user', 'phone_number': '+**********'}
        mock_update.return_value = {"success": True, "message": "Status updated"}
        
        update_data = {
            "order_id": "POS001",
            "status": OrderStatus.CONFIRMED
        }
        
        response = client.put("/pos/v1/update_order_status", json=update_data, headers=auth_headers_pos)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True


class TestAPIRoutes:
    """Test external API routes with token validation."""

    @pytest.mark.api
    @patch('app.middlewares.token_validation.TokenValidationService.validate_token')
    @patch('app.core.token_validation_core.validate_token_and_get_orders')
    def test_get_orders_with_token(self, mock_get_orders, mock_validate, client):
        """Test getting orders with token validation."""
        mock_validate.return_value = True
        mock_get_orders.return_value = [
            {"order_id": "API001", "customer_id": "CUST-001"}
        ]
        
        response = client.get("/api/v1/orders?customer_id=CUST-001&token=valid_token")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1

    @pytest.mark.api
    @patch('app.middlewares.token_validation.TokenValidationService.validate_token')
    def test_invalid_token_rejection(self, mock_validate, client):
        """Test rejection of invalid tokens."""
        mock_validate.return_value = False
        
        response = client.get("/api/v1/orders?customer_id=CUST-001&token=invalid_token")
        
        assert response.status_code == 401
        data = response.json()
        assert "Invalid token" in data["detail"]

    @pytest.mark.api
    def test_missing_token_rejection(self, client):
        """Test rejection when token is missing."""
        response = client.get("/api/v1/orders?customer_id=CUST-001")
        
        assert response.status_code == 401
        data = response.json()
        assert "Token is required" in data["detail"]


class TestPaymentRoutes:
    """Test payment-related routes."""

    @pytest.mark.api
    @pytest.mark.razorpay
    @patch('firebase_admin.auth.verify_id_token')
    @patch('app.integrations.razorpay_service.razorpay_service.create_order')
    def test_create_payment_order(self, mock_create_order, mock_verify, client, auth_headers_app):
        """Test creating a payment order."""
        mock_verify.return_value = {'user_id': 'test_user', 'phone_number': '+**********'}
        mock_create_order.return_value = {
            "id": "order_test123",
            "amount": 9999,
            "currency": "INR",
            "status": "created"
        }
        
        payment_data = {
            "order_id": "TEST001",
            "amount": 99.99,
            "currency": "INR"
        }
        
        response = client.post("/app/v1/create_payment_order", json=payment_data, headers=auth_headers_app)
        
        assert response.status_code == 200
        data = response.json()
        assert "id" in data
        assert data["currency"] == "INR"

    @pytest.mark.api
    @pytest.mark.razorpay
    @patch('firebase_admin.auth.verify_id_token')
    @patch('app.integrations.razorpay_service.razorpay_service.verify_payment')
    def test_verify_payment(self, mock_verify_payment, mock_verify, client, auth_headers_app):
        """Test payment verification."""
        mock_verify.return_value = {'user_id': 'test_user', 'phone_number': '+**********'}
        mock_verify_payment.return_value = {"success": True, "verified": True}
        
        verification_data = {
            "razorpay_order_id": "order_test123",
            "razorpay_payment_id": "pay_test456",
            "razorpay_signature": "signature_test789"
        }
        
        response = client.post("/app/v1/verify_payment", json=verification_data, headers=auth_headers_app)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True

    @pytest.mark.api
    @pytest.mark.razorpay
    @patch('firebase_admin.auth.verify_id_token')
    @patch('app.core.order_functions.get_payment_status_for_order')
    def test_get_payment_status(self, mock_get_status, mock_verify, client, auth_headers_app):
        """Test getting payment status."""
        mock_verify.return_value = {'user_id': 'test_user', 'phone_number': '+**********'}
        mock_get_status.return_value = {
            "order_id": "TEST001",
            "payment_status": PaymentStatus.COMPLETED,
            "amount": Decimal("99.99")
        }
        
        response = client.get("/app/v1/payment_status?order_id=TEST001", headers=auth_headers_app)
        
        assert response.status_code == 200
        data = response.json()
        assert data["order_id"] == "TEST001"


class TestWebhookRoutes:
    """Test webhook endpoints."""

    @pytest.mark.api
    @pytest.mark.razorpay
    @patch('app.integrations.razorpay_service.razorpay_service.verify_webhook_signature')
    @patch('app.core.order_functions.update_payment_status')
    def test_razorpay_webhook_payment_captured(self, mock_update_payment, mock_verify_signature, client):
        """Test Razorpay webhook for payment captured event."""
        mock_verify_signature.return_value = True
        mock_update_payment.return_value = {"success": True}
        
        webhook_payload = {
            "event": "payment.captured",
            "payload": {
                "payment": {
                    "entity": {
                        "id": "pay_test123",
                        "order_id": "order_test456",
                        "amount": 9999,
                        "status": "captured"
                    }
                }
            }
        }
        
        headers = {
            "X-Razorpay-Signature": "test_signature",
            "Content-Type": "application/json"
        }
        
        response = client.post("/webhooks/v1/razorpay", json=webhook_payload, headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "processed"

    @pytest.mark.api
    @pytest.mark.razorpay
    @patch('app.integrations.razorpay_service.razorpay_service.verify_webhook_signature')
    def test_razorpay_webhook_invalid_signature(self, mock_verify_signature, client):
        """Test Razorpay webhook with invalid signature."""
        mock_verify_signature.return_value = False
        
        webhook_payload = {
            "event": "payment.captured",
            "payload": {}
        }
        
        headers = {
            "X-Razorpay-Signature": "invalid_signature",
            "Content-Type": "application/json"
        }
        
        response = client.post("/webhooks/v1/razorpay", json=webhook_payload, headers=headers)
        
        assert response.status_code == 401
        data = response.json()
        assert "Invalid signature" in data["detail"]

    @pytest.mark.api
    @pytest.mark.razorpay
    def test_razorpay_webhook_missing_signature(self, client):
        """Test Razorpay webhook with missing signature."""
        webhook_payload = {
            "event": "payment.captured",
            "payload": {}
        }
        
        response = client.post("/webhooks/v1/razorpay", json=webhook_payload)
        
        assert response.status_code == 400
        data = response.json()
        assert "Missing signature" in data["detail"]


class TestErrorHandling:
    """Test error handling across all endpoints."""

    @pytest.mark.api
    def test_404_not_found(self, client):
        """Test 404 error for non-existent endpoints."""
        response = client.get("/non-existent-endpoint")
        
        assert response.status_code == 404

    @pytest.mark.api
    def test_405_method_not_allowed(self, client):
        """Test 405 error for wrong HTTP methods."""
        response = client.delete("/health")  # Health endpoint doesn't support DELETE
        
        assert response.status_code == 405

    @pytest.mark.api
    @patch('firebase_admin.auth.verify_id_token')
    def test_422_validation_error(self, mock_verify, client, auth_headers_app):
        """Test 422 error for invalid request data."""
        mock_verify.return_value = {'user_id': 'test_user', 'phone_number': '+**********'}
        
        invalid_order_data = {
            "customer_id": "",  # Empty customer_id should cause validation error
            "items": []  # Empty items should cause validation error
        }
        
        response = client.post("/app/v1/create_order", json=invalid_order_data, headers=auth_headers_app)
        
        assert response.status_code == 422

    @pytest.mark.api
    @patch('firebase_admin.auth.verify_id_token')
    @patch('app.core.order_functions.create_order_core')
    def test_500_internal_server_error(self, mock_create_order, mock_verify, client, sample_order_data, auth_headers_app):
        """Test 500 error handling."""
        mock_verify.return_value = {'user_id': 'test_user', 'phone_number': '+**********'}
        mock_create_order.side_effect = Exception("Database connection failed")
        
        response = client.post("/app/v1/create_order", json=sample_order_data, headers=auth_headers_app)
        
        assert response.status_code == 500
