"""
Database and model tests.

This module tests:
- Database connection and session management
- SQLAlchemy ORM models
- Database constraints and relationships
- Data integrity and validation
- Migration compatibility
"""

import pytest
from decimal import Decimal
from datetime import datetime, timedelta
from sqlalchemy import text
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session

from app.models.orders import Order, OrderItem, OrderAddress
from app.models.payments import PaymentDetails
from app.core.constants import OrderStatus, PaymentStatus
from app.connections.database import get_db, get_read_db


class TestDatabaseConnection:
    """Test database connection and session management."""

    @pytest.mark.database
    def test_database_connection(self, db_session):
        """Test basic database connectivity."""
        result = db_session.execute(text("SELECT 1 as test_value"))
        row = result.fetchone()
        assert row.test_value == 1

    @pytest.mark.database
    def test_database_timezone(self, db_session):
        """Test database timezone configuration."""
        result = db_session.execute(text("SHOW timezone"))
        timezone = result.fetchone()[0]
        assert "Asia/Kolkata" in timezone

    @pytest.mark.database
    def test_session_rollback(self, db_session):
        """Test session rollback functionality."""
        # Create an order
        order = Order(
            random_prefix="TEST",
            customer_id="CUST-001",
            customer_name="Test Customer",
            facility_id="FAC-001",
            facility_name="Test Facility",
            status=OrderStatus.OPEN,
            total_amount=Decimal("99.99"),
            order_mode="app"
        )
        db_session.add(order)
        db_session.flush()  # Flush to get ID but don't commit
        
        order_id = order.id
        assert order_id is not None
        
        # Rollback the session
        db_session.rollback()
        
        # Order should not exist after rollback
        found_order = db_session.query(Order).filter(Order.id == order_id).first()
        assert found_order is None

    @pytest.mark.database
    def test_connection_pooling(self, test_session_factory):
        """Test connection pooling behavior."""
        sessions = []
        
        # Create multiple sessions
        for _ in range(5):
            session = test_session_factory()
            sessions.append(session)
            
            # Test each session works
            result = session.execute(text("SELECT 1"))
            assert result.fetchone()[0] == 1
        
        # Close all sessions
        for session in sessions:
            session.close()


class TestOrderModel:
    """Test Order model functionality."""

    @pytest.mark.database
    def test_create_order(self, db_session):
        """Test creating a basic order."""
        order = Order(
            random_prefix="TEST",
            customer_id="CUST-001",
            customer_name="Test Customer",
            facility_id="FAC-001",
            facility_name="Test Facility",
            status=OrderStatus.OPEN,
            total_amount=Decimal("99.99"),
            order_mode="app"
        )
        
        db_session.add(order)
        db_session.commit()
        db_session.refresh(order)
        
        assert order.id is not None
        assert order.order_id is not None
        assert order.order_id.startswith("TEST")
        assert order.customer_id == "CUST-001"
        assert order.total_amount == Decimal("99.99")

    @pytest.mark.database
    def test_order_computed_order_id(self, db_session):
        """Test computed order_id field."""
        order = Order(
            random_prefix="COMP",
            customer_id="CUST-002",
            customer_name="Test Customer 2",
            facility_id="FAC-002",
            facility_name="Test Facility 2",
            status=OrderStatus.OPEN,
            total_amount=Decimal("150.00"),
            order_mode="pos"
        )
        
        db_session.add(order)
        db_session.commit()
        db_session.refresh(order)
        
        # order_id should be computed as random_prefix + id
        expected_order_id = f"COMP{order.id}"
        assert order.order_id == expected_order_id

    @pytest.mark.database
    def test_order_required_fields(self, db_session):
        """Test order creation with missing required fields."""
        # Missing customer_id should raise IntegrityError
        with pytest.raises(IntegrityError):
            order = Order(
                random_prefix="TEST",
                # customer_id missing
                customer_name="Test Customer",
                facility_id="FAC-001",
                facility_name="Test Facility",
                status=OrderStatus.OPEN,
                total_amount=Decimal("99.99"),
                order_mode="app"
            )
            db_session.add(order)
            db_session.commit()

    @pytest.mark.database
    def test_order_default_values(self, db_session):
        """Test order default values."""
        order = Order(
            random_prefix="DEF",
            customer_id="CUST-003",
            customer_name="Test Customer 3",
            facility_id="FAC-003",
            facility_name="Test Facility 3",
            total_amount=Decimal("75.00"),
            order_mode="web"
            # status and is_approved not provided - should use defaults
        )
        
        db_session.add(order)
        db_session.commit()
        db_session.refresh(order)
        
        assert order.status == 10  # Default status
        assert order.is_approved is False  # Default is_approved

    @pytest.mark.database
    def test_order_timestamps(self, db_session):
        """Test automatic timestamp creation."""
        order = Order(
            random_prefix="TIME",
            customer_id="CUST-004",
            customer_name="Test Customer 4",
            facility_id="FAC-004",
            facility_name="Test Facility 4",
            status=OrderStatus.OPEN,
            total_amount=Decimal("200.00"),
            order_mode="app"
        )
        
        before_create = datetime.utcnow()
        db_session.add(order)
        db_session.commit()
        db_session.refresh(order)
        after_create = datetime.utcnow()
        
        assert order.created_at is not None
        assert order.updated_at is not None
        assert before_create <= order.created_at <= after_create
        assert before_create <= order.updated_at <= after_create


class TestOrderItemModel:
    """Test OrderItem model functionality."""

    @pytest.mark.database
    def test_create_order_item(self, db_session):
        """Test creating order items."""
        # First create an order
        order = create_test_order(db_session)
        
        # Create order item
        item = OrderItem(
            order_id=order.id,
            sku="ITEM-001",
            quantity=2,
            unit_price=Decimal("25.00"),
            sale_price=Decimal("29.99"),
            product_name="Test Product",
            status=OrderStatus.OPEN
        )
        
        db_session.add(item)
        db_session.commit()
        db_session.refresh(item)
        
        assert item.id is not None
        assert item.order_id == order.id
        assert item.sku == "ITEM-001"
        assert item.quantity == 2
        assert item.unit_price == Decimal("25.00")

    @pytest.mark.database
    def test_order_item_relationship(self, db_session):
        """Test relationship between Order and OrderItem."""
        # Create order with items
        order = create_test_order(db_session)
        
        item1 = OrderItem(
            order_id=order.id,
            sku="ITEM-001",
            quantity=1,
            unit_price=Decimal("10.00"),
            sale_price=Decimal("12.00"),
            product_name="Product 1",
            status=OrderStatus.OPEN
        )
        
        item2 = OrderItem(
            order_id=order.id,
            sku="ITEM-002",
            quantity=2,
            unit_price=Decimal("15.00"),
            sale_price=Decimal("18.00"),
            product_name="Product 2",
            status=OrderStatus.OPEN
        )
        
        db_session.add_all([item1, item2])
        db_session.commit()
        
        # Test relationship
        db_session.refresh(order)
        assert len(order.items) == 2
        assert item1 in order.items
        assert item2 in order.items

    @pytest.mark.database
    def test_order_item_foreign_key_constraint(self, db_session):
        """Test foreign key constraint on order_id."""
        # Try to create item with non-existent order_id
        with pytest.raises(IntegrityError):
            item = OrderItem(
                order_id=99999,  # Non-existent order ID
                sku="ITEM-001",
                quantity=1,
                unit_price=Decimal("10.00"),
                sale_price=Decimal("12.00"),
                product_name="Test Product",
                status=OrderStatus.OPEN
            )
            db_session.add(item)
            db_session.commit()


class TestOrderAddressModel:
    """Test OrderAddress model functionality."""

    @pytest.mark.database
    def test_create_order_address(self, db_session):
        """Test creating order address."""
        # First create an order
        order = create_test_order(db_session)
        
        # Create address
        address = OrderAddress(
            order_id=order.id,
            full_name="John Doe",
            phone_number="+1234567890",
            address_line1="123 Test Street",
            address_line2="Apt 4B",
            city="Test City",
            state="Test State",
            postal_code="12345",
            country="india",
            type_of_address="home",
            longitude=Decimal("77.1234567"),
            latitude=Decimal("28.1234567")
        )
        
        db_session.add(address)
        db_session.commit()
        db_session.refresh(address)
        
        assert address.id is not None
        assert address.order_id == order.id
        assert address.full_name == "John Doe"
        assert address.country == "india"

    @pytest.mark.database
    def test_address_relationship(self, db_session):
        """Test relationship between Order and OrderAddress."""
        order = create_test_order(db_session)
        
        address = OrderAddress(
            order_id=order.id,
            full_name="Jane Doe",
            phone_number="+1234567890",
            address_line1="456 Test Avenue",
            city="Test City",
            state="Test State",
            postal_code="54321",
            country="india",
            type_of_address="work"
        )
        
        db_session.add(address)
        db_session.commit()
        
        # Test relationship
        db_session.refresh(order)
        assert len(order.addresses) == 1
        assert order.addresses[0] == address
        assert address.order == order


class TestPaymentDetailsModel:
    """Test PaymentDetails model functionality."""

    @pytest.mark.database
    def test_create_payment(self, db_session):
        """Test creating payment record."""
        payment = PaymentDetails(
            order_id="TEST001",
            payment_id="pay_test123",
            amount=Decimal("99.99"),
            currency="INR",
            status=PaymentStatus.PENDING,
            payment_method="razorpay"
        )
        
        db_session.add(payment)
        db_session.commit()
        db_session.refresh(payment)
        
        assert payment.id is not None
        assert payment.order_id == "TEST001"
        assert payment.payment_id == "pay_test123"
        assert payment.amount == Decimal("99.99")
        assert payment.status == PaymentStatus.PENDING

    @pytest.mark.database
    def test_payment_unique_constraints(self, db_session):
        """Test unique constraints on payment fields."""
        # Create first payment
        payment1 = PaymentDetails(
            order_id="TEST001",
            payment_id="pay_unique123",
            amount=Decimal("50.00"),
            currency="INR",
            status=PaymentStatus.PENDING,
            payment_method="razorpay"
        )
        db_session.add(payment1)
        db_session.commit()
        
        # Try to create another payment with same payment_id
        with pytest.raises(IntegrityError):
            payment2 = PaymentDetails(
                order_id="TEST002",
                payment_id="pay_unique123",  # Same payment_id
                amount=Decimal("75.00"),
                currency="INR",
                status=PaymentStatus.PENDING,
                payment_method="razorpay"
            )
            db_session.add(payment2)
            db_session.commit()

    @pytest.mark.database
    def test_payment_decimal_precision(self, db_session):
        """Test decimal precision for payment amounts."""
        payment = PaymentDetails(
            order_id="TEST001",
            payment_id="pay_precision123",
            amount=Decimal("123.456789"),  # High precision
            currency="INR",
            status=PaymentStatus.PENDING,
            payment_method="razorpay"
        )
        
        db_session.add(payment)
        db_session.commit()
        db_session.refresh(payment)
        
        # Should be rounded to 2 decimal places
        assert payment.amount == Decimal("123.46")


class TestDatabaseIndexes:
    """Test database indexes and query performance."""

    @pytest.mark.database
    def test_order_indexes(self, db_session):
        """Test that order indexes are working."""
        # Create multiple orders
        orders = []
        for i in range(10):
            order = Order(
                random_prefix="IDX",
                customer_id=f"CUST-{i:03d}",
                customer_name=f"Customer {i}",
                facility_id=f"FAC-{i % 3:03d}",  # 3 different facilities
                facility_name=f"Facility {i % 3}",
                status=OrderStatus.OPEN if i % 2 == 0 else OrderStatus.CONFIRMED,
                total_amount=Decimal(f"{100 + i}.00"),
                order_mode="app"
            )
            orders.append(order)
        
        db_session.add_all(orders)
        db_session.commit()
        
        # Test customer_id index
        customer_orders = db_session.query(Order).filter(
            Order.customer_id == "CUST-005"
        ).all()
        assert len(customer_orders) == 1
        
        # Test facility_id index
        facility_orders = db_session.query(Order).filter(
            Order.facility_id == "FAC-001"
        ).all()
        assert len(facility_orders) > 0
        
        # Test status index
        open_orders = db_session.query(Order).filter(
            Order.status == OrderStatus.OPEN
        ).all()
        assert len(open_orders) == 5  # Half of the orders


# Helper function to create test orders
def create_test_order(db_session: Session, **kwargs) -> Order:
    """Create a test order in the database."""
    order_data = {
        "random_prefix": "TEST",
        "customer_id": "CUST-001",
        "customer_name": "Test Customer",
        "facility_id": "FAC-001",
        "facility_name": "Test Facility",
        "status": OrderStatus.OPEN,
        "total_amount": Decimal("99.99"),
        "order_mode": "app",
        **kwargs
    }
    
    order = Order(**order_data)
    db_session.add(order)
    db_session.commit()
    db_session.refresh(order)
    return order
