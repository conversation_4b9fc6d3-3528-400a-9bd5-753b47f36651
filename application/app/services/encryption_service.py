from Crypto.Cipher import AES
from Crypto.Util.Padding import pad
import base64
import os
import logging
import binascii

logger = logging.getLogger(__name__)

KEY_SIZE = 16
# Get encryption key from environment variable, with fallback for development
ENCRYPTION_KEY_STR = os.getenv("ENCRYPTION_KEY", "thisisasamplepas")
# Ensure the key is exactly 16 bytes for AES-128
ENCRYPTION_KEY = ENCRYPTION_KEY_STR.encode('utf-8')[:KEY_SIZE].ljust(KEY_SIZE, b'\0')

class EncryptionService:
    """Service for AES encryption using CBC mode with PKCS7 padding."""

    @staticmethod
    def encrypt(plaintext: str, passphrase: bytes, iv: bytes) -> str:
        """
        Encrypt plaintext using AES-128 in CBC mode.
        The key and IV must be 16 bytes long.
        """
        try:
            # Convert plaintext string to bytes
            plaintext_bytes = plaintext.encode('utf-8')
            
            # Create a new AES cipher object
            aes = AES.new(passphrase, AES.MODE_CBC, iv)
            
            # Apply PKCS7 padding and encrypt the plaintext
            padded_text = pad(plaintext_bytes, AES.block_size, style='pkcs7')
            encrypted = aes.encrypt(padded_text)
            
            # Base64 encode the ciphertext for a string representation
            return base64.b64encode(encrypted).decode('utf-8')
        except Exception as e:
            logger.error(f"Encryption exception in encrypt(): {e}")
            raise Exception("Failed to encrypt plaintext")

    @staticmethod
    def encrypt_customer_code(customer_code: str) -> tuple[str, str]:
        """
        Encrypt customer code and return encrypted text with IV.
        
        Returns:
            tuple: (encrypted_text, iv_hex)
        """
        try:
            # The key is already defined as 16 bytes in the class
            key = ENCRYPTION_KEY

            # Generate a cryptographically secure, 16-byte random IV
            iv_bytes = os.urandom(AES.block_size)
            
            # Encrypt customer code
            encrypted_text = EncryptionService.encrypt(customer_code, key, iv_bytes)
            
            # Convert the raw IV bytes to a hexadecimal string for storage/transmission
            iv_hex = binascii.hexlify(iv_bytes).decode('utf-8')
            
            logger.info(f"Successfully encrypted customer code")
            return encrypted_text, iv_hex
            
        except Exception as e:
            logger.error(f"An exception occurred while encrypting customer code: {e}")
            raise Exception("Failed to encrypt customer code")