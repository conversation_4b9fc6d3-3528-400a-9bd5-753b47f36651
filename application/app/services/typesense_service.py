import os
import logging
from typing import Dict, Optional
import httpx
from fastapi import HTTPException

logger = logging.getLogger(__name__)


class TypesenseService:
    
    def __init__(self):
        self.host = os.getenv("TYPESENSE_HOST", "localhost")
        self.port = os.getenv("TYPESENSE_PORT", "8108")
        self.protocol = os.getenv("TYPESENSE_PROTOCOL", "http")
        self.api_key = os.getenv("TYPESENSE_API_KEY")
        self.collection_name = os.getenv("TYPESENSE_COLLECTION_NAME", "products")
        
        if not self.api_key:
            raise ValueError("TYPESENSE_API_KEY environment variable is required")
        
        self.base_url = f"{self.protocol}://{self.host}:{self.port}"
        self.headers = {
            "X-TYPESENSE-API-KEY": self.api_key,
            "Content-Type": "application/json"
        }
    
    async def get_product_by_sku(self, sku: str, facility_name: str) -> Optional[Dict]:
        try:
            # Note: facility_name from payload maps to facility_code field in Typesense
            search_params = {
                "q": sku,
                "query_by": "child_sku",
                "filter_by": f"child_sku:={sku} && facility_code:={facility_name}",
                "per_page": 1
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.base_url}/collections/{self.collection_name}/documents/search",
                    headers=self.headers,
                    params=search_params,
                    timeout=10.0
                )
                
                if response.status_code == 200:
                    data = response.json()
                    hits = data.get("hits", [])
                    
                    if hits:
                        product = hits[0]["document"]
                        logger.info(f"Found product for SKU {sku}")
                        return product
                    else:
                        logger.warning(f"Product not found for SKU {sku}")
                        return None
                        
                elif response.status_code == 404:
                    logger.warning(f"Product not found for SKU {sku}")
                    return None
                else:
                    logger.error(f"Typesense API error for SKU {sku}: {response.status_code} - {response.text}")
                    raise HTTPException(
                        status_code=500, 
                        detail=f"Failed to fetch product data for SKU {sku}"
                    )
                    
        except httpx.TimeoutException:
            logger.error(f"Timeout while fetching product data for SKU {sku}")
            raise HTTPException(
                status_code=500, 
                detail=f"Timeout while fetching product data for SKU {sku}"
            )
        except Exception as e:
            logger.error(f"Error fetching product data for SKU {sku}: {str(e)}")
            raise HTTPException(
                status_code=500, 
                detail=f"Error fetching product data for SKU {sku}"
            )
    
    async def validate_product_price(self, sku: str, facility_name: str, payload_price: float) -> bool:
        product = await self.get_product_by_sku(sku, facility_name)
        
        if not product:
            raise ValueError(f"Stock not available for facility {facility_name} and sku {sku}")
        
        typesense_price = product.get("selling_price") or product.get("price")
        
        if typesense_price is None:
            logger.error(f"No price found in Typesense for SKU {sku}")
            raise HTTPException(
                status_code=500, 
                detail=f"Price not available for SKU {sku}"
            )
        
        try:
            typesense_price = float(typesense_price)
        except (ValueError, TypeError):
            logger.error(f"Invalid price format in Typesense for SKU {sku}: {typesense_price}")
            raise HTTPException(
                status_code=500, 
                detail=f"Invalid price format for SKU {sku}"
            )
        
        price_match = abs(payload_price - typesense_price) < 0.01
        
        if not price_match:
            logger.warning(
                f"Price mismatch for SKU {sku}: payload={payload_price}, typesense={typesense_price}"
            )
        
        return price_match
    
    def extract_item_fields(self, product: Dict) -> Dict:
        return {
            # Tax fields - map from Typesense field names
            "cgst": float(product.get("tax", 0.0)),  # tax maps to cgst
            "sgst": float(product.get("sgst", 0.0)),
            "igst": float(product.get("igst", 0.0)),
            "cess": float(product.get("cess", 0.0)),
            # Price fields
            "selling_price_net": float(product.get("selling_price_net", 0.0)),
            # Return policy fields
            "is_returnable": bool(product.get("is_returnable", False)),
            "return_type": str(product.get("return_type", "00")),
            "return_window": int(product.get("return_window", 0))
        }
