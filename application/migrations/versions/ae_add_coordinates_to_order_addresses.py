"""add_coordinates_to_order_addresses

Revision ID: ae_add_coordinates
Revises: 0be2f20fe890
Create Date: 2025-08-06 12:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ae_add_coordinates'
down_revision: Union[str, Sequence[str], None] = '0be2f20fe890'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Add longitude and latitude columns to order_addresses table."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('order_addresses', sa.Column('longitude', sa.DECIMAL(precision=10, scale=7), nullable=True))
    op.add_column('order_addresses', sa.Column('latitude', sa.DECIMAL(precision=10, scale=7), nullable=True))
    
    # Add index for geographical queries
    op.create_index('idx_order_addresses_coordinates', 'order_addresses', ['longitude', 'latitude'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Remove longitude and latitude columns from order_addresses table."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('idx_order_addresses_coordinates', table_name='order_addresses')
    op.drop_column('order_addresses', 'latitude')
    op.drop_column('order_addresses', 'longitude')
    # ### end Alembic commands ###
